/*!
 * Copyright 2019 yangjunbao <<EMAIL>>. All rights reserved.
 * @since 2019-07-09 12:02:42
 */
import { app } from '@ekuaibao/whispered'
import { addNcpcCode } from '@ekuaibao/lib/lib/addNcpcCode'
import { callback } from '../index.v0'
import { newHomeConfig } from '../newHomeConfig'
import { HuaWeiSDK } from '@ekuaibao/sdk-bridge/sdk/huawei'
import registerAPI from '../registerAPI'
import { initializeTitle } from '../initializeTitle'
import initPlatformInfo from '../initPlatformInfo'
import systemLimitLogout from '../systemLimitLogout'
import registerHandler from '../registerHandler'
import { FetchError, fetchHandleError } from '../FetchConfig'
import { initRoute } from '../initRoute'
import { initColors } from '../initColors'
import setNetworkAdapter from '../setNetworkAdapter'
import initLogger from '../../logger/initLogger'
initLogger()
/********注册平台类型*******/
window.__PLANTFORM__ = 'HUAWEI'

function startup() {
  app.use({
    id: '@app',
    onready() {
      addNcpcCode()
    }
  })
  callback()
}

function unAuthorizedAction(error: FetchError) {
  if (error.status === 401) {
    // 未登录
    // goto('/login')
    location.replace(window.HUAWEI_LOGIN)
  }
}

fetchHandleError(unAuthorizedAction)

export async function bootstrap() {
  initColors()
  app.container.set('IDeviceType', 'DESKTOP')
  app.container.set('ISMessageEntry', false)
  app.sdk = app.container.get(HuaWeiSDK)

  registerHandler()

  await (app.sdk as HuaWeiSDK).initialize()
  initRoute()
  initPlatformInfo()
  systemLimitLogout()
  await newHomeConfig()

  initializeTitle()
  registerAPI()
  startup()
  setNetworkAdapter()
}

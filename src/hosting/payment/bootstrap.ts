/*!
 * Copyright 2019 yangjunbao <yangjun<PERSON>@shimo.im>. All rights reserved.
 * @since 2019-07-09 12:05:45
 */
import { app, render, renderAsApp } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'
import './crytoagent'
import { init } from './init'
import { filterFromUrl as filter } from '../../lib/lib-util'
import setNetworkAdapter from '../setNetworkAdapter'

import { initColors } from '../initColors'
import initLogger from '../../logger/initLogger'
initLogger()
function startup() {
  const pluginType = filter('pluginType')
  console.log(pluginType)
  switch (pluginType) {
    case 'paymentV2':
      app.use(require('../../plugins/changjiepayment/indexV2').default)
      break
    case 'payment':
      app.use(require('../../plugins/changjiepayment/index').default)
      break
    case 'certification':
      app.use(require('../../plugins/chanpay-payment/index').default)
      break
  }
  
  app.callback().then(dom => renderAsApp(dom))
}

export async function bootstrap() {
  initColors()
  try {
    await init()
    setNetworkAdapter()
    document.title = i18n.get(`易快报@@${Fetch.ekbCorpId}`, null, () => i18n.get('易快报专业版'))
    startup()
  } catch (e) {
    if (e.type === 'login') {
      alert(e.msg)
      window.close()
    }
    console.log(e)
  }
}

import React from 'react'
import Money from '../../../../elements/puppet/Money'
import EkbHighLighter from '../../../../elements/EkbHighLighter'
import { getDateDiff } from '@ekuaibao/lib/lib/lib-util'
import styles from './NewBillItem.module.less'
import { get } from 'lodash'
import { getMyBillApproveStatus } from './utils'
import { billStateMap } from './staticUtil'
import classnames from 'classnames'
import { Tooltip } from '@hose/eui'
import { getStaffShowByConfig } from '../../../../elements/utilFn'
import { getSpecificationName } from '../../util/billUtils'
import { app as api } from '@ekuaibao/whispered'
const { startOpenFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')
const avatar = require('../../../../images/avatar.svg')

export enum PlanType {
  nomal = 'NOMAL',
  countersign = 'COUNTERSIGN'
}

export enum OperatorType {
  person = 'PERSON',
  system = 'SYSTEM',
  ebot = 'EBOT'
}

interface SpecificationProps extends StringAnyProps {
  name: string
  id: string
}

interface StaffProps extends StringAnyProps {
  name: string
}
interface NewListItemProps extends StringAnyProps {
  isSelect: boolean
  dataSource: any
  searchKey: string
  onClick: (param: any) => void
  isMyBill: boolean
  isShowCode?: boolean
}

interface FlowProps {
  id: string
  state: string
}

interface FormProps {
  title: string
  type: string
  code: string
  amount: string
  specification: SpecificationProps
}

interface PlanProps {
  type: PlanType
  urgent: boolean
}

interface OperatorProps {
  count: number
  type: OperatorType
  staff: StaffProps
}

export interface MyBillCardItemProps {
  flow: FlowProps
  form: FormProps
  plan: PlanProps
  operator: OperatorProps
}

export default class NewBillItem extends React.Component<NewListItemProps, {}> {
  static defaultProps = {
    isMyBill: true,
    isShowCode: true
  }

  handleClickItem = () => {
    const { onClick, dataSource } = this.props
    if (typeof onClick === 'function') {
      startOpenFlowPerformanceStatistics()
      onClick(dataSource)
    }
  }

  renderMyBills = () => {
    const { dataSource, searchKey, isSelect, isShowCode } = this.props
    const { form, flow, plan } = dataSource
    const submitDate = get(form, 'submitDate', '')
    const { name, str } = getMyBillApproveStatus(dataSource)
    const { state, payingFailure = false, systemGeneration = false, subsidyGeneration } = flow
    const { isUrgent } = plan
    const { color, icon } = billStateMap()[state] || {}
    const cls = classnames(styles['new-bill-item-wrapper'], { [styles['active']]: isSelect })
    const alterFlag = flow.alterFlag >= '1'

    let showMark = false
    const samePerson = flow?.ownerId?.id === form?.submitterId?.id ? true : false
    if (!samePerson) showMark = true

    return (
      <div className={cls} onClick={this.handleClickItem}>
        <div className="bill-item-log-wrap">
          <span className="bill-item-log" style={{ color: color }}>
            <div>{icon}</div>
            {!!name && <span className="operator-span">{name}</span>}
            <span className={'ml-2'}>{str}</span>
          </span>
          <span className="placeholder fs-12">{getDateDiff(submitDate)}</span>
        </div>
        <div className="horizontal"> 
          <div className="bill-item-info">
            <div className="bill-title-wrapper">
              {isUrgent && <span className="urgent-color">{i18n.get('[加急]')}</span>}
              {payingFailure &&  <div className="bill-item-warning">{i18n.get('[支付失败]')}</div>}
              <div className="bill-title overflow-text bold">
                <div className="ekb-highlight-box text-nowrap-ellipsis">
                  <Tooltip title={form.title || i18n.get('[无标题]')} placement="topLeft">
                    <span>
                      <EkbHighLighter
                        highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
                        searchWords={[searchKey]}
                        textToHighlight={form.title || i18n.get('[无标题]')}
                      />
                    </span>
                  </Tooltip>
                </div>
              </div>
            </div>
            <div className="bill-template-type">
              {isShowCode && (
                <EkbHighLighter
                  highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
                  searchWords={[searchKey]}
                  textToHighlight={form.code}
                />
              )}
              <span className="bill-title overflow-text">{getSpecificationName(form?.specification)}</span>
                {(subsidyGeneration === 'surplus' || !!systemGeneration) && (
                  <div className="placeholder ml-5">{i18n.get('自动创建')}</div>
                )}
                {alterFlag && 
                <div className="placeholder ml-5">{i18n.get('变更')}</div>
                }
                 {showMark && <span className="placeholder ml-5">{i18n.get('委托')}</span>}
            </div>
          </div>
          <div className="bill-item-right">
          {form.amount && Number(form.amount) !== 0 ? (
            <Money className="bold" class symbolSize={14} valueSize={14} value={form.amount} isShort={true} />
          ) : (
            <span className="bold">{i18n.get('暂无金额')}</span>
          )}
        </div>
        </div>
      </div>
    )
  }

  renderOthers = () => {
    const { dataSource, searchKey, isSelect } = this.props
    const form = get(dataSource, 'flowId.form', '')
    const isApproved = get(dataSource, 'isApproved', '')
    const title = get(form, 'title', '')
    const submitter = get(form, 'submitterId', '')
    const submitDate = get(form, 'submitDate', '')
    const formType = get(dataSource, 'flowId.formType', '')
    const specificationName = getSpecificationName(form?.specificationId)
    const amount = (form[formType + 'Money'] && form[formType + 'Money'].standard) || null
    return (
      <div className={`${styles['billList-wrap']} ${isSelect ? styles['active'] : ''}`} onClick={this.handleClickItem}>
        <img src={submitter.avatar || avatar} />
        <div className="bill-content">
          <div className="bill-content-top">
            <EkbHighLighter
              highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
              searchWords={[searchKey]}
              textToHighlight={getStaffShowByConfig(submitter)}
            />
            <span>{i18n.get('提交')}</span>
            <span>{getDateDiff(submitDate)}</span>
          </div>
          <div className="bill-content-middle">
            <span>
              {dataSource.isUrgent && <span className="urgent-color">{i18n.get('[加急]')}</span>}
              {searchKey ? (
                <EkbHighLighter
                  highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
                  searchWords={[searchKey]}
                  textToHighlight={form.title || i18n.get('[无标题]')}
                />
              ) : (
                title
              )}
            </span>
            <span className="bold">
              {amount && Number(amount) !== 0 ? <Money value={amount}  isShowChinese={true} /> : i18n.get('暂无金额')}
            </span>
          </div>
          <div className="bill-content-bottom">
            <EkbHighLighter
              highlightStyle={{ color: 'var(--brand-base)', background: 'none' }}
              searchWords={[searchKey]}
              textToHighlight={form.code}
            />
            <span className="specificationName">{specificationName}</span>
            {isApproved && <span className="state">{i18n.get('已审批')}</span>}
          </div>
        </div>
      </div>
    )
  }

  render() {
    const { isMyBill } = this.props
    return <>{isMyBill ? this.renderMyBills() : this.renderOthers()}</>
  }
}

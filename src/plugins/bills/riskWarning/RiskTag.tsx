/*
 * <AUTHOR> zing-lh
 * @LastEditTime : 2024-09-11 14:14:26
 * @Description  : 风险tag，目前用于表单主体上的风险展示
 * 描述功能/使用范围/注意事项
 */
import React,{ useState } from 'react'
import { Popover } from '@hose/eui'
import { renderPopoverContent } from '../../../components/layout/ExceedStandardRiskForField'
import { some, uniq } from 'lodash'
import { riskType, RiskWarningItem } from './type'
import riskStyle from './index.module.less'
import { OutlinedDirectionRight, OutlinedDirectionDown, FilledTipsWarning } from '@hose/eui-icons'
import classnames from 'classnames'
import { flattenObject } from './utils'
import { isErrorItem } from './ai-audit-result/utils'


interface Props {
    external:RiskWarningItem[],
    style:React.CSSProperties
}

 const RiskTag = (props:Props)=>{
    const {external = [], style = {} }  = props
    const [arrowDirection, setArrowDirection] = useState('')
    const flattenRisk = flattenObject(external) || [] // 分摊风险是object{[]}
    const num = flattenRisk?.length || 0
    const isForbid = some(flattenRisk, (item) => isErrorItem(item))
    const typeString = flattenRisk?.map(v=>{
      return i18n.get(riskType[v.type]) || v.type
    })

    const RiskContent = (
      <div className={riskStyle['risk-tag-Popover']}>
        {renderPopoverContent(flattenRisk)}
      </div>
    )

    const visibleChange = (visible) =>{
        visible ? setArrowDirection('top') : setArrowDirection('')
    }

    return (
      <Popover
        trigger="hover"
        overlayClassName="risk-popover"
        arrowPointAtCenter
        autoAdjustOverflow
        content={RiskContent}
        onOpenChange = {open => visibleChange(open)}
      >
        <div className={riskStyle['risk-tag-new']} style={style}>
          <div className={classnames('inner-wrapper',{'error':isForbid})}>
            <FilledTipsWarning className={`icon`}/>
            <span className="number">{i18n.get(`有 {__k0} 处{__k1}风险`, { __k0: num,__k1:uniq(typeString).join('、') })}</span>
            <span className="more">{arrowDirection === 'top' ? <OutlinedDirectionDown className="ml-2" /> : <OutlinedDirectionRight className="ml-2" />}</span>
          </div>
        </div>
      </Popover>
    )
  }

  export default RiskTag
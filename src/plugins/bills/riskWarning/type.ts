export interface RiskWarningItem {
  id: null | string
  controlField: string
  path: string
  pathValueId: string
  isOutOfLimitReject: boolean
  controlVersion: number
  controlName: string
  messages: string[]
  type: string
  isLink: boolean
  title: null | string
  prices: null | any
  ruleDetail: string
  riskTag: string
  messagesV2?: any // 额外属性
  isBudgetOccupy?: boolean // 预算占用属性
  riskLevel?: 'PASS' | 'WARNING' | 'ERROR' // 状态
}

  export interface RiskWarningData {
    riskWarningV2: RiskWarningItem[],
    flowId:string
  }

  export const riskType = {
    'budget':'预算',
    'dataLinkLedger':'台账',
    'costControl':'费用标准',
    'expense':'易购',
    'submit':'提交次数',
    'remunerationInfo':'酬金申报',
    'invoice':'发票',
    'loan':'借款',
    'loanRemain':'借款',
    'requisitionInfo':'报销金额',
    'invoiceReview':'发票复核',
    'trip':'行程',
    'dataLinkFilter':'业务对象',
    'flowQuery':'单据联查',
    'aiApproval':'AI' // AI 审批
  }

  export enum RiskTypeEnum {
    Budget = 'budget',
    CostControl = 'costControl',
    DataLinkLedger = 'dataLinkLedger',
    Invoice = 'invoice'
  }
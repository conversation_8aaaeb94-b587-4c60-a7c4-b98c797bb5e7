/**
 * 费用明细列表风险数据统计展示
 * 适用于风险集合数据 展示不同种风险type数据
 */
import React, { Fragment, useState, memo } from 'react'
import styles from './DetailRiskTip.module.less'
import { some } from 'lodash'
import { Popover, Tag } from '@hose/eui'
import RiskWarningItem from './RiskWarningItem'
import RiskWarningBudgetItem from './RiskWarningBudgetItem'
import { OutlinedDirectionRight, OutlinedDirectionDown, FilledTipsWarning } from '@hose/eui-icons'
import { riskType, RiskTypeEnum, RiskWarningItem as RiskWarningItemIF } from './type'
import { flattenObject } from './utils'
import classnames from 'classnames'
import { renderCommentWithMarkdown } from '../../../elements/ekbc-business/bills/FlowLogItemWrapper'
import { isErrorItem } from './ai-audit-result/utils'

interface Props {
  style: React.CSSProperties
  external: any
  RiskPromptOptimization: Boolean
  riskInfo: any
  children: React.ReactNode
  className?: string
}

export default memo(function DetailRiskTipCopy(props: Props) {
  const [arrowDirection, setArrowDirection] = useState('')
  const [activePopover, setActivePopover] = useState('')
  const { style, external = {}, RiskPromptOptimization, riskInfo = {} } = props
  const detailsRisk = flattenObject(external)
  const num = detailsRisk?.length || 0
  const detailsRiskType = {}

  detailsRisk.forEach((item: RiskWarningItemIF) => {
    if (item?.type) {
      if (!detailsRiskType[item.type]) {
        detailsRiskType[item.type] = []
      }
      detailsRiskType[item.type].push(item)
    }
  })

  // 预算占用详情 兼容历史逻辑（费用明细模板配置出来）没有实质的字段只能依据字段属性兼容 riskInfo有值并且为amount，并且type为Budget以及没有其他风险
  if (!detailsRiskType[RiskTypeEnum.Budget]) {
    const amountRiskInfo = riskInfo['amount']
    if (amountRiskInfo && amountRiskInfo.length) {
      amountRiskInfo.forEach(item => {
        if (item.type === RiskTypeEnum.Budget) {
          if (!detailsRiskType[item.type]) {
            detailsRiskType[item.type] = []
          }
          detailsRiskType[item.type].push({ ...item, isBudgetOccupy: true })
        }
      })
    }
  }

  const visibleChange = (visible, type) => {
    visible ? setArrowDirection('top') : setArrowDirection('')
    setActivePopover(type)
  }

  if (!num && !Object.keys(riskInfo).length) {
    return <Fragment>{props.children}</Fragment>
  }

  return (
    <div className={classnames(styles['risk-wrapper'], props.className)} onClick={e => e.stopPropagation()}>
      {Object.keys(detailsRiskType)?.map((item, index) => {
        const riskDetails: RiskWarningItemIF[] = detailsRiskType[item]
        const isForbid = some(riskDetails, isErrorItem)

        if (RiskPromptOptimization) {
          return (
            <div className={classnames('risk-item', { error: isForbid })}>
              <FilledTipsWarning className={`icon`} />
              <span className="number width-auto">
                {i18n.get(riskType[item] || item)} {riskDetails?.length}
              </span>
            </div>
          )
        }
        return (
          <Popover
            overlayClassName="risk-popover"
            autoAdjustOverflow={true}
            content={<PopoverContent riskMap={detailsRiskType} type={item} />}
            placement="bottom"
            onOpenChange={open => visibleChange(open, item)}
            key={index}
          >
            {item === RiskTypeEnum.Budget && riskDetails[0]?.isBudgetOccupy ? (
              <div className="budget-info-wrapper">
                {`${i18n.get('预算占用详情')} `}
                {riskDetails[0]?.prices?.length}
                {activePopover === item && arrowDirection === 'top' ? (
                  <OutlinedDirectionDown className="ml-2" />
                ) : (
                  <OutlinedDirectionRight className="ml-2" />
                )}
              </div>
            ) : (
              <div className={classnames('risk-item', { error: isForbid })}>
                <FilledTipsWarning className={`icon`} style={style} />
                <span className={'number'}>
                  {i18n.get(`有 {__k0} 处{__k1}风险`, {
                    __k0: riskDetails?.length,
                    __k1: i18n.get(riskType[item] || item)
                  })}
                </span>
                <span className="more">
                  {activePopover === item && arrowDirection === 'top' ? (
                    <OutlinedDirectionDown className="ml-2" />
                  ) : (
                    <OutlinedDirectionRight className="ml-2" />
                  )}
                </span>
              </div>
            )}
          </Popover>
        )
      })}
    </div>
  )
})

const PopoverContent = ({ riskMap, type }) => {
  const currentRisk = riskMap?.[type]

  return (
    <>
      <div className="risk-title">
        {currentRisk[0]?.isBudgetOccupy ? i18n.get('预算占用详情') : i18n.get('风险详情')}
      </div>
      <ul className="risk-message-list">
        {currentRisk?.map((risk: RiskWarningItemIF) => {
          const hasMessageV2 = risk?.messagesV2 && risk?.messagesV2.length > 0
          const isBudgetRisk = !!risk?.prices && risk.type === 'budget'
          const isCostControl = risk?.type === 'costControl'
          const riskTypeString = riskType[risk.type] || risk.type
          const isForbid = isErrorItem(risk)
          const controlName = risk?.controlName
          const messages = risk?.messages
          return (
            <li className="risk-message-item" key={controlName}>
              {isBudgetRisk && <RiskWarningBudgetItem risk={risk} />}
              {!isBudgetRisk && !hasMessageV2 && isCostControl && <RiskWarningItem risk={risk} />}
              {!isCostControl && !isBudgetRisk && (
                <p className="risk-name">
                  {riskTypeString && (
                    <Tag color="warning" fill="invert" className="mr-8" size="small">
                      {i18n.get(riskTypeString)}
                    </Tag>
                  )}
                  <span>{controlName}</span>
                </p>
              )}
              {!isBudgetRisk && !hasMessageV2 && !isCostControl && Boolean(messages?.length) && (
                <div className={`risk-message ${isForbid ? 'error' : ''}`}>
                  {messages?.map((msg, index) => (
                    <div key={msg}>
                      {messages.length === 1 ? renderCommentWithMarkdown(msg, msg, {type: 'risk'}) : `${index + 1}、` + msg}
                    </div>
                  ))}
                </div>
              )}
              {!isBudgetRisk &&
                hasMessageV2 &&
                risk?.messagesV2.map(item => <RiskMessageItemV2 key={item.invoiceMsg} data={item} />)}
            </li>
          )
        })}
      </ul>
    </>
  )
}

const RiskMessageItemV2 = props => {
  const { data } = props

  return (
    <div className="risk-item-v2">
      <div className="flex-container">
        <div className="risk-item-v2-label">{i18n.get('风险信息')}:</div>
        <div className="flex-1 risk-item-v2-info">{data.invoiceMsg}</div>
      </div>
      {!!data.invoiceRiskExplainContent && (
        <div className="flex-container">
          <div className="risk-item-v2-label">{i18n.get('原因')}:</div>
          <div className="flex-1 risk-item-v2-info">{data.invoiceRiskExplainContent}</div>
        </div>
      )}
      {!!data.relatedFlows?.length && (
        <div className="flex-container">
          <div className="risk-item-v2-label">{i18n.get('相关单据')}:</div>
          <div className="flex-1 risk-item-v2-info">
            {data.relatedFlows
              .map(item => `${item.flowCode}${item.invoiceNum ? `(${item.invoiceNum})` : ''}`)
              .join('，')}
          </div>
        </div>
      )}
    </div>
  )
}

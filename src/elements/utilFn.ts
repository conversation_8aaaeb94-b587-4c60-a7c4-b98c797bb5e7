import moment from 'moment'
import { app as api } from '@ekuaibao/whispered'
import { getContentByLocale } from '@ekuaibao/lib/lib/i18n-helpers'
import { isBaseData, isCity, isMoney } from '@ekuaibao/lib/lib/propertySetIs'
import { getMoney } from '../lib/misc'
import { isString } from '@ekuaibao/helpers'

const isPayNode = node => node.name === i18n.get('出纳支付节点') || node.name === '出纳支付'

// 是否显示别名作为用户标识
export const isConfigShowNickName = () => {
  const config = api.getState('@common.organizationConfig')
  if (!config.applyToGlobal) {
    return false
  }
  return config.staffDisplayConfig?.[0] === 'nickName'
}

const showNickName = (value) => {
  return value?.nickName || '-'
}

/**
 * 根据配置获取员工名称（配置在通讯录内）
*/
const getStaffName = (value) => {
  return isConfigShowNickName() ? showNickName(value) : getDisplayName(value)
}

/**
 * 根据配置获取员工完整的名称显示（姓名 + 配置的第二字段）
 */
const getStaffShowByConfig = value => {
  if (!value) {
    return value
  }
  let staffObj = value
  if (isString(value)) {
    const staffMap = api?.getState()['@common']?.allStaffActiveStaffMap || {}
    staffObj = staffMap[value]
  }
  const name = getStaffName(staffObj)
  if (!name) return ''
  const str = getStaffShowExternal(staffObj)
  return str ? `${name}${str}` : name
}

export const getStaffShowExternal = (staff) => {
  const config = api.getState('@common.organizationConfig') //取配置参数
  if (!config.applyToGlobal) {
    return ''
  }
  let showValue = getStaffShowValue(staff, config.staffDisplayConfig)
  if (!showValue && staff && config.staffDisplayConfig?.length > 1) {
    const staffMap = api?.getState()['@common']?.allStaffActiveStaffMap || {}
    const staffObj = staffMap[staff.id || staff]
    if (staffObj) {
      showValue = getStaffShowValue(staffObj, config.staffDisplayConfig)
    }
  }
  return showValue
}

export const getDisplayName = (obj) => {
  const label = getContentByLocale(obj, 'label')
  const name = getContentByLocale(obj, 'name')
  return label || name
}

const isDate = (propertySet: any) => {
  return propertySet?.dataType?.type === 'date'
}

const isText = (propertySet: any) => {
  return propertySet?.dataType?.type === 'text'
}

const isNumber = (propertySet: any) => {
  return propertySet?.dataType?.type === 'number'
}


const getCityValue = (customPropertyData: string) => {
  try {
    const dataArr = JSON.parse(customPropertyData)
    return dataArr.map(c => c.label).join(',')
  } catch {
    return ''
  }
}

const getMoneyValue = (customPropertyData: any) => {
  const { standardSymbol, standard, standardUnit } = customPropertyData;
  return `${standardSymbol}${standard}${standardUnit}`
}

const getDateValue = (customPropertyData: any) => {
  return moment(customPropertyData).format('YYYY-MM-DD')
}

const getStaffCustomFieldValues = (customFieldName: string, staffId: string) => {
  const allProperties = api.getState('@common.staffProperties') || []
  const customProperty = allProperties.find(p => p.name === customFieldName)
  let value = ''
  if (customProperty) {
    // 改接口中的 staffDisplayDimensions 包含了 档案字段的相关信息
    const staffs = api.getState('@common.staffsActives').data || {}
    const staff = staffs.find(s => s.id === staffId)
    if (staff) {
      const { staffDisplayDimensions, staffCustomForm } = staff;
      const customPropertyData = staffCustomForm?.[customFieldName];
      if (!customPropertyData) {
        return ''
      }
      if (isCity(customProperty)) {
        value = getCityValue(customPropertyData)
      } else if (isMoney(customProperty)) {
        value = getMoneyValue(customPropertyData)
      } else if (isDate(customProperty)) {
        value = getDateValue(customPropertyData)
      } else if (isText(customProperty)) {
        // 文本类型的配置即为值
        value = customPropertyData
      } else if (isNumber(customProperty)) {
        value = customPropertyData + customProperty.dataType?.unit || ''
      } else {
        // 在自定义档案类型
        const customPropertyDataRef = staffDisplayDimensions?.[customPropertyData]
        value = getDisplayName({ label: customPropertyDataRef?.fullName, name: customPropertyDataRef?.enFullName })
      }
    }
  }
  return value
}

const getStaffShowValue = (value, staffDisplayConfig) => {
  let showValue = ''
  if (staffDisplayConfig.length > 1) {
    const str = staffDisplayConfig[1]
    if (str === 'deptCode') {
      const departmentMapData = api.getState('@common.department.mapData') || {}
      const externalDepartmentMapData = api.getState('@common.externalDepartment.mapData') || {}
      const deptData = { ...departmentMapData, ...externalDepartmentMapData }
      let deptId = typeof value.defaultDepartment === 'object' ? value.defaultDepartment.id : value.defaultDepartment
      if (!deptId) {
        deptId =
          value.externalDefaultDepartment && typeof value.externalDefaultDepartment === 'object'
            ? value.externalDefaultDepartment.id
            : value.externalDefaultDepartment
      }
      const depValue = deptData[deptId]
      if (depValue) {
        showValue = depValue.code
          ? `${i18n.get('（')}${depValue.name}-${depValue.code}${i18n.get('）')}`
          : `${i18n.get('（')}${depValue.name}${i18n.get('）')}`
      }
    } else {
      if (value) {
        if (value[str]) {
          showValue = `${i18n.get('（')}${value[str]}${i18n.get('）')}`
        } else if (value.staffCustomForm) {
          const fieldValue = getStaffCustomFieldValues(str, value.id)
          showValue = fieldValue ? `${i18n.get('（')}${fieldValue}${i18n.get('）')}` : ''
        }
      }
    }
  }
  return showValue
}

const getStaffExplainValue = staff => {
  if (!staff) return ''
  const config = api.getState('@common.organizationConfig') //取配置参数
  if (!config.applyToGlobal) {
    return ''
  }
  const str = getStaffShowValue(staff, config.staffDisplayConfig)
  const status = staff.active === false ? `(${i18n.get('已停用')})` : ``
  return `${str}${status}`
}

const getDimensionExplainValue = dimension => {
  if (!dimension) {
    return ''
  }
  const code = dimension.code
  return !!code ? `(${code})` : ''
}

export const getUserQueryField = () => {
  const isDisplayingNickName = isConfigShowNickName()
  return isDisplayingNickName ? 'nickName' : 'name'
}

export const filterStaff = (searchKey, staff) => {
  const isDisplayingNickName = isConfigShowNickName()
  if (isDisplayingNickName) {
    return likeMatch(staff.nickName, searchKey)
  }
  return likeMatch(staff.name, searchKey) ||
    likeMatch(staff.nameInitial, searchKey) ||
    likeMatch(staff.nameSpell, searchKey)
}

export const filterDepartment = (searchKey, department) => {
  return likeMatch(department.name, searchKey) ||
    likeMatch(department.nameInitial, searchKey) ||
    likeMatch(department.nameSpell, searchKey)
}

export const likeMatch = (sourceStr, matchKey) => {
  if (!sourceStr) {
    return false
  }
  return sourceStr.toLowerCase().includes(matchKey?.toLowerCase())
}

export { isPayNode, getStaffShowValue, getStaffShowByConfig, getStaffExplainValue, getDimensionExplainValue, getStaffName }

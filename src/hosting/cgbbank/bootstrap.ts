import { app, renderAsApp } from '@ekuaibao/whispered'
import { Fetch } from '@ekuaibao/fetch'
import './crytoagent'
import { init } from './init'
import { filterFromUrl as filter } from '../../lib/lib-util'
import setNetworkAdapter from '../setNetworkAdapter'

import { initColors } from '../initColors'
function startup() {
  const pluginType = filter('pluginType')
  console.log(pluginType)
  app.use(require('../../plugins/cgbpayment/index').default)

  app.callback().then(dom => renderAsApp(dom))
}

export async function bootstrap() {
  initColors()
  try {
    await init()
    setNetworkAdapter()
    document.title = i18n.get(`易快报@@${Fetch.ekbCorpId}`, null, () => i18n.get('易快报专业版'))
    startup()
  } catch (e) {
    if (e.type === 'login') {
      alert(e.msg)
      window.close()
    }
    console.log(e)
  }
}

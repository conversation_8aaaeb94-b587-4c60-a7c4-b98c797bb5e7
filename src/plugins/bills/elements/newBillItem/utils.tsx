import { MyBillCardItemProps, OperatorType, PlanType } from './'
import { billStateMap } from './staticUtil'
import { getStaffName } from '../../../../elements/utilFn'

export function getMyBillApproveStatus(myBillItem: MyBillCardItemProps): { name: string; str: string } {
  const { flow, plan, operator } = myBillItem
  const { state } = flow
  const { count, type, staff } = operator
  const { label } = billStateMap()[state] || {}
  let str = label
  let name = ''

  if (type === OperatorType.person && !!staff) {
    name = getStaffName(staff) //  getStaffShowByConfig(staff)  单据列表不需要根据配置展示，来自需求
  }
  if (type === OperatorType.ebot) {
    name = 'ebot'
    str = i18n.get('驳回单据')
  }
  if (type === OperatorType.system) {
    name = ''
    str = i18n.get('超时自动驳回')
  }
  if (plan.type === PlanType.countersign) {
    name = ''
    str = i18n.get('人会签中', { count })
  }

  return { name, str }
}

// 时间戳转 多久前
export function getDateDiff(dateTimeStamp: any) {
  const minute = 1000 * 60
  const hour = minute * 60
  const day = hour * 24
  const month = day * 30
  const now = new Date().getTime()
  const diffValue = now - dateTimeStamp
  if (diffValue < 0) {
    // 若日期不符则弹出窗口告之
    console.log(i18n.get('结束日期不能小于开始日期！'))
  }
  const yearC = diffValue / (month * 12)
  const monthC = diffValue / month
  const weekC = diffValue / (7 * day)
  const dayC = diffValue / day
  const hourC = diffValue / hour
  const minC = diffValue / minute
  let result = ''
  if (yearC >= 1) {
    result = i18n.get('年前', { time: parseInt(yearC) })
  } else if (monthC >= 1) {
    result = i18n.get('个月前', { time: parseInt(monthC) })
  } else if (weekC >= 1) {
    result = i18n.get('周前', { time: parseInt(weekC) })
  } else if (dayC >= 1) {
    result = i18n.get('天前', { time: parseInt(dayC) })
  } else if (hourC >= 1) {
    result = i18n.get('个小时前', { time: parseInt(hourC) })
  } else if (minC >= 30) {
    result = i18n.get('半小时前')
  } else if (minC >= 15) {
    result = i18n.get('分钟前', { time: 15 })
  } else if (minC >= 10) {
    result = i18n.get('分钟前', { time: 10 })
  } else if (minC >= 5) {
    result = i18n.get('分钟前', { time: 5 })
  } else if (minC >= 3) {
    result = i18n.get('分钟前', { time: 3 })
  } else {
    result = i18n.get('刚刚')
  }
  return result
}

import React from 'react'
import moment from 'moment'
import { get } from 'lodash'
import { toJS } from 'mobx'
import { Tag, Popover, Space, Tooltip } from '@hose/eui'
import { app } from '@ekuaibao/whispered'
import { fnFilePreview, fnFileDownload } from '../puppet/details/DetailsItemBase'
import { fnFormatAttachment } from '@ekuaibao/lib/lib/lib-util'
import AttachmentList from '../puppet/attachmentList/AttachmentList'
import Money from '../../elements/puppet/Money'
import { renderContent, renderStyle, GetInvoiceStateText } from '../puppet/details/FormatDateUtils'
import buildDataLinkEditsStr from '../../components/utils/fnFormatDataLinkEdits'
import { fixPayeeInfoNew } from '../data-grid/columnOthers'
import { formatDateTime } from '../../components/utils/fnPredefine4Date'
import { getDisplayName } from "../utilFn";
import { getInvoiceTagColorByType, invoiceFormMap } from '../puppet/details/FormatDateUtils'
import { isStaffs } from '@ekuaibao/lib/lib/propertySetIs'
import { getTagLabel } from '../../elements/util'

import styles from './columnRender.module.less';

export const NullCell = function({ style }) {
  return <span style={style}>-</span>
}

const InvoiceColumn = ({
  line = {},
  record,
  index,
  renderInvoiceColumn,
  invoiceImageMap,
  isInvoiceManagePermissions,
  onChange
}) => {
  let detailId = get(record, 'feeTypeForm.detailId')
  let invoiceImageList = invoiceImageMap[detailId] || []
  if (!record) {
    return <NullCell />
  }
  let invoiceColorCls = ''
  if (line.type === 'exist' || line.type === 'unify') {
    invoiceColorCls = 'color-blue3'
  }

  return (
    <div
      onClick={e => {
        e = e || window.event
        e.stopPropagation()
        e.nativeEvent.stopImmediatePropagation()
        return false
      }}
    >
      <Popover
        overlayClassName="invoice-popover"
        overlayStyle={renderStyle(line)}
        title={line.invoiceCorporation ? i18n.get('开票方') : void 0}
        content={renderContent(toJS(line), null, invoiceImageList, isInvoiceManagePermissions, onChange)}
      >
        <Tag fill="outline" size="small" color={getInvoiceTagColorByType(line.type)}>
          <GetInvoiceStateText invoiceFormState={line.type} invoiceForm={line} />
        </Tag>
      </Popover>
    </div>
  )
}

function getInvoiceInfo(dataSource) {
  let renderInvoiceColumn = []
  dataSource.forEach(v => {
    if (v.feeTypeForm && v.feeTypeForm.invoiceForm) {
      let t = v.feeTypeForm.invoiceForm
      if (t.attachments || t.fileList) {
        let attachments = t.attachments || t.fileList
        let arr = Array.isArray(attachments) ? attachments : [attachments]
        let att = fnFormatAttachment(arr)
        let attachmentsContent = (
          <AttachmentList
            onFilePreview={fnFilePreview.bind(this, att)}
            onFileDownload={fnFileDownload}
            fileList={att}
          />
        )
        renderInvoiceColumn.push({ attachmentsContent: attachmentsContent })
      } else if (t.invoiceCorporation) {
        renderInvoiceColumn.push({ invoiceCorporation: t.invoiceCorporation, type: t.type })
      } else {
        if (!t.invoiceDetail && t.master && t.details) {
          t.invoiceDetail = {}
          t.invoiceDetail.details = t.details
          t.invoiceDetail.master = t.master
        }
        renderInvoiceColumn.push({ invoiceDetail: t.invoiceDetail, type: t.type })
      }
    }
  })
  return renderInvoiceColumn
}

const isMultiStaffRefField = (fieldInfo) => {
  return fieldInfo.type === 'list' &&
    // 元素类型是人员
    isStaffs({ dataType: fieldInfo.elemType || {} })
}

export const isStaffRefField = (fieldObj) => {
  const sourceFieldInfo = (app.getState('@common.globalFields.data') || []).find(v => v.name === fieldObj.field) || {}
  // 单选人员 或 多选人员
  return isStaffs(sourceFieldInfo) || isMultiStaffRefField(sourceFieldInfo.dataType || {})
}

export const renderMap = (
  field,
  dataSource,
  riskWarning,
  isForbid,
  invoiceImageMap,
  onVisibleChange,
  isInvoiceManagePermissions,
  onChange
) => {
  let renderInvoiceColumn = getInvoiceInfo(dataSource)
  return (type, allTemplate) => {
    switch (type) {
      case 'money':
        return (money, record) => {
          const external = get(riskWarning, get(record, 'feeTypeForm.detailId'))
          if (!money) {
            return <NullCell style={{ display: 'inline-block', textAlign: 'right'}} />
          }
          return (
            <div className={styles['money-renderer-wrapper']} style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
              <span style={{ minWidth: '30px' }}>{get(money, 'standardStrCode', '')}</span>
              <Money withoutStyle={true} value={money} showSymbol={false} />
            </div>
          )
        }

      case 'date':
        return value => {
          if (!value) {
            return <NullCell />
          }
          const { withTime, dateTimeType } = field
          const dateFormat = formatDateTime(withTime, dateTimeType)
          value = moment(value).format(dateFormat)
          return <span>{value}</span>
        }
      case 'select':
      case 'select_search':
        return (value) => {
          if (!value) {
            return <NullCell />
          }

          if (isStaffRefField(field) && value) {
            const staffs = Array.isArray(value) ? value : [value]
            return <Space size={4} wrap>
              {
                staffs.map((staff) => {
                  return <Tag style={{paddingLeft: 4,  color: 'var(--eui-text-title)'}} round key={staff.id}>{getTagLabel(staff, true)}</Tag>
                })
              }
            </Space>
          }

          let title = value

          if (typeof title === 'object') {
            title = getDisplayName(value)
            title = value.active !== undefined && !value.active ? i18n.get(`{__k0}（已停用）`, { __k0: title }) : title
          }
          if (Array.isArray(value)) {
            title = value.reduce((prev, cur, index) => {
              const name = getDisplayName(cur)
              const codeStr = cur.code ? `${i18n.get('（')}${cur.code}${i18n.get('）')}` : ''
              return `${prev}${index === 0 ? '' : i18n.get('、')}${name || ''}${codeStr}`
            }, '')
            title = !!title ? title : '-'
          }

          return <span>{title}</span>
        }

      case 'dateRange':
        return value => {
          if (!value || (typeof value === 'string' && !value.length)) {
            return <NullCell />
          }

          let start = 0
          let end = 0
          if (Array.isArray(value)) {
            start = value[0]
            end = value[1]
          } else if (typeof value === 'object') {
            start = value.start
            end = value.end
          }
          const { withTime, dateTimeType } = field
          const dateFormat = formatDateTime(withTime, dateTimeType)

          start = moment
            .utc(start)
            .utcOffset(8)
            .format(dateFormat)
          end = moment
            .utc(end)
            .utcOffset(8)
            .format(dateFormat)

          return <span>{`${start} - ${end} `}</span>
        }

      case 'city':
        return vlaue => {
          if (!vlaue) {
            return <NullCell />
          }

          const arr = JSON.parse(vlaue)
          const label = arr.map(line => line.label).join(',')
          return <span>{label}</span>
        }

      case 'switcher':
        return value => {
          let title = ''
          if (value === undefined) {
            title = '-'
          } else {
            title = value ? i18n.get('是') : i18n.get('否')
          }

          return <div>{title}</div>
        }

      case 'textarea':
        return value => {
          if (!value) {
            return <NullCell />
          }

          return (
            <Tooltip title={value}>
              <div
                style={{
                  maxWidth: '260px',
                }}
                className={styles['two-line-ellipsis']}
              >
                {value}
              </div>
            </Tooltip>
          )
        }

      case 'invoice':
        return (line, record, index) => {
          const data = {
            line,
            record,
            index,
            renderInvoiceColumn,
            invoiceImageMap,
            isInvoiceManagePermissions,
            onChange
          }
          return <div>{<InvoiceColumn {...data} />}</div>
        }

      case 'payeeInfo':
        return value => {
          if (!value) {
            return <NullCell />
          }
          return fixPayeeInfoNew().render(value)
        }
      case 'dataLinkEdits':
        return value => {
          if (!value) {
            return <NullCell />
          }
          return <div>{buildDataLinkEditsStr(field, value, allTemplate, true)}</div>
        }
      default:
        return value => {
          // 类型判断兼容
          if (!value || typeof value === 'object') {
            return <NullCell />
          }
          return <div>{value}</div>
        }
    }
  }
}

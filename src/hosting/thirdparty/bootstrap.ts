import { app as api } from '@ekuaibao/whispered'
import { addNcpcCode } from '@ekuaibao/lib/lib/addNcpcCode'
import { app, callback } from '../index.v0'
import FSOpenSDK from '@ekuaibao/sdk-bridge/sdk/fsopen'
import registerAPI from '../registerAPI'
import { initializeTitle } from '../initializeTitle'
import initPlatformInfo from '../initPlatformInfo'
import systemLimitLogout from '../systemLimitLogout'
import qs from 'qs'
import { newHomeConfig } from '../newHomeConfig'
import registerHandler from '../registerHandler'
import { fetchHandleError } from '../FetchConfig'
import { initRoute } from '../initRoute'
import { initColors } from '../initColors'
import { Fetch, FetchError } from '@ekuaibao/fetch'
import setNetworkAdapter from '../setNetworkAdapter'
import { session } from '@ekuaibao/session-info'
require('../../styles/app.less')
import initLogger from '../../logger/initLogger'
import { initDatafluxRum } from '../dataflux-rum'

initDatafluxRum()
initLogger()
/********注册平台类型*******/
window.__PLANTFORM__ = 'THIRDPARTY'

// qs提供的默认decode会把+号替换成空格
const params = qs.parse(location.search.slice(1), { decoder: str => decodeURIComponent(str) })

// 重定向
function redirect() {
  switch (params['pageType']) {
    case 'payment':
      return '/audit/payment'
    case 'home':
      return '/bills'
    case 'frontPage':
      return '/new-homepage'
    case 'approve':
      return '/audit/approve'
    case 'receiveExpress':
      return '/audit/receiveExpress'
    case 'invoiceReview':
      return '/invoice-review'
    case 'form':
      const { backlogId, flowId } = params
      return `/backlogDetail/${!!backlogId}/${backlogId || flowId}`
    case 'customPathname':
      return params['customPathname']
    case 'backlogDetail':
      return '/billEntryDetail'
    case 'edit':
      return '/billEntryDetail'
    case 'expenseTracker':
      return '/record-expends' //随手记
  }
  return void 0
}

function startup() {
  const params = qs.parse(location.search.slice(1))
  if (params?.billentry) {
    window.__SUB_PLANNTFORM__ = 'BILLENTRY'
  }
  app.use({
    id: '@thirdparty',
    onready() {
      addNcpcCode()
    }
  })

  callback()
}

fetchHandleError((error: FetchError) => {
  if (error.status === 401) {
    let path = '/web/errorInfo.html?errorMessage=授权失败，请重新登录'
    if (location.href.includes(':9999')) {
      path = '/errorInfo.html?errorMessage=授权失败，请重新登录'
    }
    location.replace(path)
  }
})

export const bootstrap = async () => {
  initColors()
  app.container.set('IDeviceType', 'DESKTOP')
  app.container.set('ISMessageEntry', false)
  app.sdk = app.container.get(FSOpenSDK)

  if (params['accessToken']) {
    Fetch.accessToken = params['accessToken']
  }
  registerHandler()

  const user = session.get('user') || {}
  if (params['ekbCorpId'] && user?.corpId !== params['ekbCorpId']) {
    session.set('user', { ...user, corpId: params['ekbCorpId'] })
  }

  if (window.IS_SMG) {
    const user = session.get('user')
    const href = window.location.href || ''
    const flag = href.indexOf('flowId=') === -1
    if (flag && user && user.corpId && user.corpId !== Fetch.ekbCorpId) {
      Fetch.ekbCorpId = user.corpId
    }
  }
  if ((params['urlCorpId'] || params['ekbCorpId'] || params['corpId']) && window.__PLANTFORM__ === 'THIRDPARTY') {
    const user = session.get('user')
    if (!user?.isChangeCorp) {
      session.set('user', {
        corpId: params['urlCorpId'] || params['ekbCorpId'] || params['corpId'],
        accessToken: params['accessToken']
      })
    }
  }

  initRoute()
  await (app.sdk as FSOpenSDK).initialize()
  setNetworkAdapter()
  initPlatformInfo()
  systemLimitLogout()
  await newHomeConfig()

  initializeTitle()
  registerAPI()
  startup()
  const page = redirect()
  api.go(page)
}

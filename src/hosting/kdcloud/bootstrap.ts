/*!
 * Copyright 2019 yangjunbao <yangjun<PERSON>@shimo.im>. All rights reserved.
 * @since 2019-07-09 12:12:18
 */
import { addNcpcCode } from '@ekuaibao/lib/lib/addNcpcCode'
import { app, callback } from '../index.v0'
import { newHomeConfig } from '../newHomeConfig'
import KDCloudSDK from '@ekuaibao/sdk-bridge/sdk/kdcloud'
import registerAPI from '../registerAPI'
import { initialize } from '../i18n-initialize'
import { initializeTitle } from '../initializeTitle'
import initPlatformInfo from '../initPlatformInfo'
import systemLimitLogout from '../systemLimitLogout'
import registerHandler from '../registerHandler'
import { initRoute } from '../initRoute'
import { initColors } from '../initColors'
import setNetworkAdapter from '../setNetworkAdapter'
import registerKdCloudAPI from './registerKdCloudAPI'
import initLogger from '../../logger/initLogger'
initLogger()
/********注册平台类型*******/
window.__PLANTFORM__ = 'KD_CLOUD'

// window.UPLOAD_INVOICE_FILE_URL = UPLOAD_INVOICE_FILE_URL
function startup() {
  app.use({
    id: '@kdcloud',
    onready() {
      addNcpcCode()
    }
  })

  callback()
}

function modifierKey(e: any) {
  let modifier = 0
  if (e.metaKey) {
    modifier |= 1
  }
  if (e.ctrlKey) {
    modifier |= 2
  }
  if (e.altKey) {
    modifier |= 4
  }
  if (e.shiftKey) {
    modifier |= 8
  }
  return modifier
}

window.addEventListener('keydown', function(e: KeyboardEvent) {
  const modifier = modifierKey(e)
  if (e.keyCode === 82 && (modifier === 1 || modifier === 2)) {
    // meta+r or ctrl+r
    window.location.reload()
  }
})

export const bootstrap = async () => {
  initColors()
  app.container.set('IDeviceType', 'DESKTOP')
  app.container.set('ISMessageEntry', false)
  app.sdk = app.container.get(KDCloudSDK)

  registerHandler()

  await (app.sdk as KDCloudSDK).initialize()
  initRoute()
  initPlatformInfo()
  systemLimitLogout()
  await newHomeConfig()

  registerAPI()
  registerKdCloudAPI()
  startup()
  initializeTitle()
  setNetworkAdapter()
}

if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'beta') {
  require('../development')
}

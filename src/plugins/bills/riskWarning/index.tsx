/*
 * <AUTHOR> zing-lh
 * @LastEditTime : 2024-09-18 11:54:38
 * @Description  : 风险组件
 * 描述功能/使用范围/注意事项
 *
 */

import React, { Component } from 'react'
import { RiskWarningItem, RiskWarningData, riskType } from './type'
import { Collapse, Tag, Button, Ellipsis, Tooltip, Avatar, SkeletonParagraph } from '@hose/eui'
import { FilledDirectionExpandRight, TwoToneLogoHoseai, FilledGeneralMember } from '@hose/eui-icons'
const { Panel } = Collapse
import styles from './index.module.less'
import { isNumber, get } from 'lodash'
import { BillStateEnum } from '../../bills/enum'
import { renderComment } from '../../../elements/ekbc-business/bills/flow-log-item'
import { renderCommentWithMarkdown } from '../../../elements/ekbc-business/bills/FlowLogItemWrapper'
import { Resource } from '@ekuaibao/fetch'
import { app as api } from '@ekuaibao/whispered'
import { ResultCard } from './result-card'
import { Card as RiskCard, Theme } from './card'
import { getStaffName } from '../../../elements/utilFn'
import { AIAgentTag } from '../../../elements/ai-agent-utils'
import AIAuditResult from './ai-audit-result/ai-audit-result'
import {  AIAuditSummary } from './ai-audit-result/ai-audit-summary'
import { isErrorItem, isWarningItem } from './ai-audit-result/utils'
import classNames from 'classnames'

interface RiskWarningProps {
  RiskWarningData: RiskWarningData // 整个风险数据源头
  bus: any // bus
  billDetails: any
  isActiveInfo: Boolean
  isLoading: boolean
  showAIAuditResult?: boolean // 是否显示AI审核结果
}

interface RiskWarningState {
  errorData: RiskWarningItem[] | null // 错误风险数据
  riskData: RiskWarningItem[] | null // 风险数据
  isLoading: false // 加载状态
  riskWarningData: any // 整个风险数据源头
  detailsActive: boolean
  allowViewLoanList: boolean
  showAIAuditResult: boolean
}
const FEE_DETAIL = 'FEE_DETAIL' // 明细

const isEmptyObject = obj => {
  return Object.keys(obj).length === 0 && obj.constructor === Object
}

export default class RiskWarning extends Component<RiskWarningProps, RiskWarningState> {
  static defaultProps = {
    RiskWarningData: []
  }
  state: RiskWarningState = {
    errorData: null,
    riskData: null,
    isLoading: false,
    detailsActive: true,
    riskWarningData: null, // 整个风险数据源头
    allowViewLoanList: false,
    showAIAuditResult: false
  }

  componentDidMount() {
    const { bus } = this.props
    // risk 数据分割，根据 isOutOfLimitReject 这个字段区分错误还是风险数据
    this.setRiskWarningData()
    this.judgeLoanViewAuth()
    bus && bus.on('active:risk:go:check', this.handleActiveRiskGo)
  }

  componentWillUnmount() {
    const { bus } = this.props
    bus && bus.un('active:risk:go:check', this.handleActiveRiskGo)
  }

  handleActiveRiskGo = isList => {
    this.setState({
      detailsActive: isList
    })
  }

  setRiskWarningData(data?: Readonly<RiskWarningProps>) {
    const { RiskWarningData } = data ? data : this.props
    const riskWarningV2 = RiskWarningData?.riskWarningV2 || []
    if (riskWarningV2) {
      const error = riskWarningV2.filter(v => {
        if (v.hasOwnProperty('riskLevel')) {
          return isErrorItem(v)
        }
        return v.isOutOfLimitReject
      })
      const waring = riskWarningV2.filter(v => {
        if (v.hasOwnProperty('riskLevel')) {
          return isWarningItem(v)
        }
        return !v.isOutOfLimitReject
      })
      this.setState({
        errorData: error,
        riskData: waring,
        riskWarningData: RiskWarningData
      })
    }
  }

  componentDidUpdate(prevProps: Readonly<RiskWarningProps>): void {
    const { riskWarningData } = this.state
    if (prevProps.RiskWarningData !== riskWarningData) {
      this.setRiskWarningData(prevProps)
    }
  }

  // 定位到某元素
  fnScrollToItemById = (id: string) => {
    const element = document.getElementById(id)
    if (element) {
      // bill-info-editable-container 编辑页面  eui-tabs-content-holder 单据提交之后的状态
      const isEdit = element.closest('#bill-info-editable-container')
      const parentElement = element.closest('#bill-info-editable-container') || element.closest('.content-wrap')
      const clientHeightY = parentElement?.clientHeight

      if (parentElement && isNumber(clientHeightY)) {
        const parentItem: HTMLDivElement = element.closest('.group-item')
        let scrollHeight = element?.offsetTop - clientHeightY / 2
        let parentItemTotalHeight = 0
        const details = element.parentElement?.querySelector('#details-container')
        const otherHeight = isEdit ? details?.clientHeight || 0 : 0

        if (parentItem) {
          const antRows = parentItem?.querySelectorAll('.form-list-item')
          let elementIndex = -1

          for (let i = 0; i < antRows?.length; i++) {
            // 确保不包括元素本身
            if (antRows[i].contains(element) || antRows[i].parentElement === element) {
              elementIndex = i
            } else if (i < elementIndex || elementIndex === -1) {
              parentItemTotalHeight += antRows[i].clientHeight
            }
          }
        }
        if (parentItem) {
          // 可视区域 - 当前屏幕一半宽度+ 所有父级元素高度 100 为冗余高度可调整
          scrollHeight = parentItem?.offsetTop - clientHeightY / 2 + parentItemTotalHeight + 100 + otherHeight
        }
        parentElement?.scrollTo({ top: scrollHeight, left: 0, behavior: 'smooth' }) // 找到父级元素滚动条向下再滚动200像素，使当前明细
      }
    }
  }

  // 去修改滚动到某个字段
  private scrollToFiled = (e, v) => {
    if (!v.controlField || !v.path) {
      return
    }
    if (v.path === FEE_DETAIL) {
      this.props?.bus?.invoke('risk:scroll:view', v)
    } else if (v.controlField === 'apportionMoney' && v.path === 'APPORTION') {
      v.pathValueId = v.pathValueId?.split('$')[0] || '' // 分摊风险是以明细id和分摊id 拼接在一起
      this.props?.bus?.invoke('risk:scroll:view', v)
    } else {
      this.fnScrollToItemById(v.controlField)
    }
    e?.nativeEvent?.stopImmediatePropagation(v.controlField)
    e.stopPropagation()
  }

  // 展示审批同意弹框
  isShowReason = () => {
    const { billDetails } = this.props
    const billLogs = billDetails?.logs
    const states = [BillStateEnum.Rejected, BillStateEnum.Approving, BillStateEnum.Sending, BillStateEnum.Receiving]
    const agreeAction = ['freeflow.send', 'freeflow.agree', 'freeflow.receive']
    let logs = null
    let lastIndexOfAgree = -1
    let lastIndexOfReject = -1
    let lastIndexOfSubmit = -1
    let dec = null
    let type = null

    if (!billDetails?.logs && !states.includes(billDetails.state)) {
      return false
    }

    for (let i = 0; i < billLogs.length; i++) {
      if (agreeAction.includes(billLogs[i].action)) {
        lastIndexOfAgree = i
      } else if (billLogs[i].action === 'freeflow.reject') {
        lastIndexOfReject = i
      } else if (billLogs[i].action === 'freeflow.submit') {
        lastIndexOfSubmit = i
      }
    }
    // 根据审批同意和驳回那个是最后一次出现则展示那个,lastIndexOfSubmit 是防止提交后出现审批同意
    if (lastIndexOfAgree > lastIndexOfReject && lastIndexOfSubmit < lastIndexOfAgree) {
      dec = '审批同意'
      type = 'agree'
      logs = (billDetails?.logs || []).filter(v => agreeAction.includes(v.action))
    } else if (lastIndexOfReject > lastIndexOfAgree && lastIndexOfSubmit < lastIndexOfReject) {
      logs = (billDetails?.logs || []).filter(v => v.action === 'freeflow.reject')
      dec = '驳回原因'
      type = 'reject'
    }

    if (logs && logs?.length) {
      const lastLog = logs[logs.length - 1]
      if (lastLog?.operatorId?.name && !lastLog?.attributes?.isEbotNode) {
        const result = get(lastLog, 'attributes.extras.result')
        const tips = get(lastLog, 'attributes.extras.tips')
        return {
          name: lastLog?.operatorId ? getStaffName(lastLog.operatorId) : '',
          auditorAvatar: lastLog?.operatorId?.avatar,
          comment: lastLog?.attributes?.comment,
          dec: dec,
          extras: {
            result,
            tips
          },
          type: type,
          lastLog: lastLog,
          participants: lastLog?.attributes?.participants,
          isAIApproval: lastLog?.attributes?.nodeType === 'AIAPPROVAL',
        }
      }
    }
    return false
  }

  fnClick = (type: string) => {
    if (type === 'loan') {
      this.props?.bus?.emit('right:risk:open:details')
    }
  }

  judgeLoanViewAuth = async () => {
    const configRule = new Resource('/api/v2/loan/config')
    const userInfo = await api.invokeService('@common:get:userinfo', {})
    const loanConfig = await configRule.GET('')

    const hasLoanManage = userInfo?.permissions?.includes("LOAN_MANAGE");
    const restrictAccessItem = loanConfig?.items?.find(item => item?.type === "RESTRICT_ACCESS")?.forbid

    if (userInfo?.staff?.id === this.props.billDetails?.plan?.submitterId) { // 自己看自己的单据
      this.setState({ allowViewLoanList: true })
    } else if (userInfo?.staff?.id === this.props.billDetails?.ownerId?.id) { // 委托人查看单据
      this.setState({ allowViewLoanList: true })
    } else {
      this.setState({ allowViewLoanList: !restrictAccessItem || hasLoanManage });
    }
  }

  renderItem = (data, isRisking = true) => {
    const { detailsActive, allowViewLoanList } = this.state
    const { billDetails, isActiveInfo = true, bus } = this.props
    let txt = '去查看'
    let riskHeaderTitle = isRisking ? '存在以下风险' : '存在以下错误'
    if ([BillStateEnum.Draft, BillStateEnum.Rejected].includes(billDetails.state)) {
      txt = '去修改'
      riskHeaderTitle = isRisking ? '存在以下风险，请酌情修改' : '存在以下错误，请务必修正'
    }

    if (Boolean(data?.length)) {
      return (
        <Panel header={i18n.get(riskHeaderTitle)} key={isRisking ? 'risk' : 'error'} className="warning-panel">
          {data.map(v => {
            console.log({ v })
            const isClick = v.controlField && v.path
            let showOpen = ['loan'].includes(v?.type) && !v.controlField
            if (v?.type === 'loan') {
              showOpen = ['loan'].includes(v?.type) && !v.controlField && allowViewLoanList
            }
            // 明细表格模式不支持定位
            const itemDisable = v.path === FEE_DETAIL ? bus?.$showType !== 'LIST' || !detailsActive : false
            const controlName =
              v?.type === 'costControl' ? `${i18n.get('当前费用超出标准：')}${v?.controlName}` : v?.controlName
            const messages = v?.messages
            const tagText = i18n.get(riskType[v.type] || v.type)
            return (
              <RiskCard className={`warning-item warning-item-${isRisking ? Theme.Warning : ''}`} theme={isRisking ? Theme.Warning : Theme.Error} key={v.id} onClick={(e) => { !showOpen ? this.scrollToFiled(e, v) : this.fnClick(v.type) }}>
                <div className="dis-f jc-sb mb-4">
                  <div className="left">
                    <Tooltip title={tagText}>
                      {v.type === 'aiApproval' ? <AIAgentTag text={tagText}/>  :  <Tag className="text-nowrap-ellipsis warning-item-tag" color={isRisking ? 'warning' : 'danger'} size="small">{tagText}</Tag>}
                    </Tooltip>
                    {v.riskTag === 'AI' && (
                      <div className="ai">
                        AI
                      </div>
                    )}
                  </div>
                  {showOpen && (
                    <Button category="text" size="mini" theme="highlight">
                      {i18n.get('详情')}
                    </Button>
                  )}
                  {isClick && !showOpen && isActiveInfo && !itemDisable && (
                    <Button category="text" size="mini" theme="highlight">
                      {i18n.get(txt)}
                    </Button>
                  )}
                </div>
                <div className="title">
                  {controlName ? controlName : messages.join('、')}
                </div>
                <div className="des mt-4">
                  {renderCommentWithMarkdown(
                    controlName ? messages.join('、') : v.ruleDetail,
                    controlName ? messages.join('、') : v.ruleDetail,
                    { type: 'risk' }
                  )}
                </div>
              </RiskCard>
            )
          })}
        </Panel>
      )
    }
    return <></>
  }

  handleAIAuditSummaryClick = () => {
    this.setState({
      showAIAuditResult: true
    })
  }

  handleAIAuditResultBack = () => {
    this.setState({
      showAIAuditResult: false
    })
  }

  render() {
    const { riskData, errorData, riskWarningData, showAIAuditResult } = this.state
    const isNotWaringRisk = !Boolean(errorData?.length) && !Boolean(riskData?.length)
    const { name, comment, lastLog, dec, type, participants, auditorAvatar, extras, isAIApproval } = this.isShowReason() || {}

    const { RiskWarningData, isLoading } = this.props

    if (isLoading && isEmptyObject(RiskWarningData)) {
      return <SkeletonParagraph className="bill-more-info-skeleton" lineCount={13} />
    }

    if (showAIAuditResult) {
      return <AIAuditResult items={RiskWarningData?.riskWarningV2 || []} onBack={this.handleAIAuditResultBack} />
    }

    return (
      <div className={styles['risk-warning-info']}>
        {comment ? (
          <div className={classNames('reject-box', type, { ['is-ai-approval']: isAIApproval, 'withExtras': extras?.result })}>
            <div className="title">
              <Avatar className='avatar' src={auditorAvatar} size="mini" shape="circle"></Avatar>
              <Tooltip title={name} placement="topLeft">
                <div className="name">
                  {name}
                </div>
              </Tooltip>
              <label className="status">{extras?.result || i18n.get(dec)}</label>
            </div>
            <div className="reason">
              {!!extras?.tips && <div className={styles['content-msg-tips']}>{extras?.tips}</div>}
              {participants?.length ? (
                renderComment(lastLog)
              ) : (
                renderCommentWithMarkdown(comment, comment)
              )}
            </div>
          </div>
        ) : null}
        {/* <div className="result-title">{i18n.get('综合审查结果')}</div>
        <div className="result">
          <div className={`box error ${!Boolean(errorData?.length) && 'zero'}`}>
            <div className="num">{errorData?.length || 0}</div>
            <div className="label">{i18n.get('错误')}</div>
          </div>
          <div className={`box ${!Boolean(riskData?.length) && 'zero'}`}>
            <div className="num">{riskData?.length || 0}</div>
            <div className="label">{i18n.get('风险')}</div>
          </div>
        </div> */}
        <ResultCard
          extra={ riskWarningData?.riskWarningV2 ? <AIAuditSummary onClick={this.handleAIAuditSummaryClick} items={riskWarningData?.riskWarningV2 || []} /> : null}
          result={{ error: errorData?.length || 0, risk: riskData?.length || 0 }}
        />
        {/* {
          isNotWaringRisk && <div className="success">
              <div className="title">{i18n.get('以下各项检验均已通过')}</div>
              <div className="success-item">
                {
                  flowAllRiskType?.map(v=>{
                    return <Tooltip title={riskType[v] || '未知'}><label><FilledTipsYes className="mr-5" />{riskType[v] || '未知'}</label></Tooltip> // <label><FilledTipsYes className="mr-5" />{riskType[v] || '未知'}</label>
                  })
                }
              </div>
          </div>
        } */}
        {!isNotWaringRisk && (
          <Collapse
            bordered={false}
            defaultActiveKey={['risk', 'error']}
            expandIcon={({ isActive }) => <FilledDirectionExpandRight rotate={isActive ? 90 : 0} />}
            className="warning-collapse"
          >
            {this.renderItem(errorData, false)}
            {this.renderItem(riskData)}
          </Collapse>
        )}
      </div>
    )
  }
}

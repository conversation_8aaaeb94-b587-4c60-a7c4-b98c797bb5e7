import React, { PureComponent, createRef } from 'react'
import styles from './DetailsWrapper.module.less'
import classnames from 'classnames'
import DetailsItem from './DetailsItem'
import DetailsEditTable from './DetailsEditTable'
import DetailsTable from '../../table/DetailsTable'
import ActiveWrapper from '../../../elements/ekbc-basic/active-wrapper/active-wrapper'
import { fnGetVisibleIds } from '../../../lib/fee-util'
import { DETAIL_SHOWTYPE } from '@ekuaibao/lib/lib/enums'
import { app as api } from '@ekuaibao/whispered'
import { get, sortedUniq, flatten, isEqual, cloneDeep, isNumber } from 'lodash'
import { isString } from '@ekuaibao/helpers'
import { EnhanceConnect } from '@ekuaibao/store'
import { fnGetInvoiceManage } from '../../invoice-form/utils/config'
import { addFullPathInApportion } from '../apportions/utils'
import { getTripEditable } from '../../../lib/lib-util'
import { sortTypeMap } from './FormatDateUtils'
import { getDetailSpeHiddenFields, getHiddenFields } from './util'
import { Popconfirm, message } from '@hose/eui'
import { OutlinedEditDeleteTrash } from '@hose/eui-icons'
import { isErrorItem } from '../../../plugins/bills/riskWarning/ai-audit-result/utils'
const getRiskFieldNum = api.invokeServiceAsLazyValue('@bills:get:RiskFieldNumFn')
const parseAsMeta = api.invokeServiceAsLazyValue('@bills:import:parseAsMeta')
let itemByname = null
const noop = () => {}

@EnhanceConnect(state => ({
  userInfo: state['@common'].userinfo.data,
  staffs: state['@common'].staffs,
  feeTypes: state['@common'].feetypes.data,
  travelBlackList: state['@bills'].travelBlackList,
  RiskPromptOptimization: state['@common'].powers.RiskPromptOptimization,
  multiplePayeesMode: state['@bills'].multiplePayeesMode
}))
export default class DetailsWrapper extends PureComponent {
  static defaultProps = {
    dataSource: [],
    onLineClick: noop,
    onAddDetailClick: noop,
    isEdit: false,
    isModify: false,
    visibleFeeTypes: [],
    selectedData: [],
    baseDataProperties: [],
    apportionVisibleList: [],
    showType: DETAIL_SHOWTYPE.LIST,
    onSortGroupsChange: noop
  }

  constructor(props) {
    super(props)
    this.order = 0
    let permissions = props.userInfo && props.userInfo.permissions
    const isInvoiceManagePermissions = !props.isEdit && fnGetInvoiceManage(permissions)
    this.detailRef = createRef([])
    this.handleDocumentClick = this.handleDocumentClick.bind(this)
    this.state = {
      hiddenFieldsMap: {}, // 只在明细列表的时候使用了，表格展示的字段没有隐藏的
      selectedData: props.selectedData,
      invoiceImageMap: {},
      isInvoiceManagePermissions,
      allTemplate: null,
      hasDataLinkEdits: false
    }
  }

  componentDidMount() {
    document.addEventListener('click', this.handleDocumentClick)
    this.updateDataSource(this.props)
    this.getTemplate(this.props)
  }

  handleDocumentClick() {
    const element = document.getElementById('details-container') || document.getElementById('details-container2')
    if (element) {
      const allChildElements = element.querySelectorAll(`.item-wrapper`)
      if (allChildElements?.length) {
        allChildElements.forEach(child => {
          child.classList.remove('danger', 'waring')
        })
      }
    }
  }

  componentWillUnmount() {
    document.removeEventListener('click', this.handleDocumentClick)
  }

  componentWillMount() {
    const { bus } = this.props
    this.updateMulRefList(this.props)
    bus?.watch('risk:scroll:view', this.scrollIntoView)
  }

  componentWillUnmount() {
    document.removeEventListener('click', this.handleDocumentClick)
  }

  componentWillReceiveProps(nextProps) {
    if (!isEqual(this.props.dataSource, nextProps.dataSource)) {
      this.updateDataSource(nextProps)
      this.getTemplate(nextProps)
    }
    if (this.props.selectedData !== nextProps.selectedData) {
      this.setSelectData(nextProps)
    }
    if (this.props.dataSource !== nextProps.dataSource) {
      this.updateMulRefList(nextProps)
    }
    if (this.props.external !== nextProps.external) {
      this.updateExteral(nextProps.external)
    }
  }
  scrollIntoView = detailId => {
    const element = this.detailRef?.current?.querySelector(`div[data-type="${detailId?.pathValueId}"]`)
    if (element) {
      this.handleDocumentClick() // 移除其他的class
      const classStyle = isErrorItem(detailId) ? 'danger' : 'waring'
      setTimeout(() => {
        const parentElement = element.closest('#bill-info-editable-container') || element.closest('.content-wrap')
        let clientHeightY = parentElement?.clientHeight || 0

        if (!parentElement) return

        clientHeightY = clientHeightY - 56 // 56是底部footer的高度

        const elementRect = element.getBoundingClientRect()
        const parentRect = parentElement.getBoundingClientRect()

        // 计算目标元素相对于父容器的顶部位置
        const scrollHeight = elementRect.top + parentElement.scrollTop - parentRect.top - clientHeightY / 2 + 48

        parentElement.scrollTo({ top: scrollHeight, left: 0, behavior: 'smooth' }) // 滚动到计算的高度

        // 添加闪动边框的类
        element.getElementsByClassName('item-wrapper')[0].classList.add(classStyle)
      }, 200)
    }
  }

  updateExteral = external => {
    api.emit('viewer-update-exteral', { external })
  }

  updateMulRefList = props => {
    const { dataSource = [], baseDataProperties } = props
    const dataTemp = {}
    let ids = []
    if (!!dataSource.length) {
      dataSource.forEach((el, index) => {
        const { feeTypeForm, specificationId } = el
        const components = parseAsMeta()(specificationId, baseDataProperties)
        const fields = components.filter(
          el => el.showInDetails && el.type && el.type.startsWith('list:ref:basedata.Dimension')
        )
        const listRef = fields.map(el => feeTypeForm[el.field])
        if (listRef.length) {
          dataTemp[index] = fields
          ids = ids.concat(listRef)
        }
      })
      ids = sortedUniq(flatten(ids).filter(id => isString(id)))
      if (ids.length) {
        api.invokeService('@bills:get:dimension', ids.join(',')).then(res => {
          dataSource.forEach((el, index) => {
            const { feeTypeForm } = el
            const fields = dataTemp[index]
            if (fields) {
              fields.forEach(field => {
                const mulRefListIds = feeTypeForm[field.name]
                if (mulRefListIds) {
                  const mulRefListObjs = mulRefListIds.map(id => res.items.find(el => el.id === id) || id)
                  if (mulRefListObjs) {
                    feeTypeForm[field.name] = mulRefListObjs
                  }
                }
              })
            }
          })
          this.setState({ dataSource })
        })
      }
    }
  }

  componentWillUnmount() {
    const { bus } = this.props
    // 组件销毁时，清掉reducer中的自定义档案的全路径
    api.invokeService('@common:save:dimension:fullPathMap')
    bus?.un('risk:scroll:view', this.scrollIntoView)
  }

  // 为dataSource中的自定义档案准备fullPathMap
  updateDataSource = props => {
    let { dataSource = [] } = props
    const fieldIds = addFullPathInApportion(dataSource) || []
    if (fieldIds.length > 0) {
      // api.invokeService('@common:get:dimensionItems:fullName', fieldIds).then(result => {
      api.invokeService('@common:get:dimensionItems:fullName:byId', fieldIds).then(result => {
        let map = {}
        result.forEach(item => {
          map[item.id] = item.fullName
        })
        api.invokeService('@common:save:dimension:fullPathMap', map)
      })
    }
  }

  setSelectData = props => {
    const { selectedData } = props
    this.setState({ selectedData })
  }

  handleTableRowClick = (item, index, event) => {
    const { onLineClick } = this.props
    onLineClick && onLineClick(item, index)
  }

  onSelectChange = (changeRows, selected) => {
    this.onChangeChecked(changeRows, selected)
  }

  onChangeChecked = (changeRows, selected) => {
    const { handleChange } = this.props
    const { selectedData } = this.state
    let data = selectedData.slice(0)
    changeRows.forEach(item => {
      if (!selected && data.find(oo => oo.idx === item.idx)) {
        const index = data.findIndex(oo => oo.idx === item.idx)
        data.splice(index, 1)
      }

      if (selected && !data.find(oo => oo.idx === item.idx)) {
        data.push(item)
      }
    })
    handleChange && handleChange(data)
  }

  fnGetVisibleIds = () => {
    const { visibleFeeTypes } = this.props
    return fnGetVisibleIds(visibleFeeTypes)
  }

  fnGetValidExternaForItem = (external, line) => {
    //获取每条消费明细对应的超标提醒
    if (!external) return void 0
    let validExternal = void 0
    for (let key in external) {
      key === line.feeTypeForm.detailId && (validExternal = external[key])
    }
    return validExternal
  }

  fnGetValidExternaForTable = (external, group) => {
    //获取不同费用类型下table对应的超标提醒
    if (!external) return void 0
    let validExternal = {}
    group.forEach(v => {
      validExternal[v.feeTypeForm.detailId] = external[v.feeTypeForm.detailId]
    })
    return Object.keys(validExternal).length > 0 ? validExternal : void 0
  }
  renderDetailsTAbleByFee = () => {
    const { dataSource = [] } = this.props
    let groups = {}
    dataSource.forEach(item => {
      if (groups[item.feeTypeId.id]) {
        groups[item.feeTypeId.id].push(item)
      } else {
        groups[item.feeTypeId.id] = []
        groups[item.feeTypeId.id].push(item)
      }
    })
    return groups
  }
  renderDetailsTableKind = level => {
    const { feeTypes, dataSource = [] } = this.props
    let groups = {}
    if (level === 'E' || level === 'P') {
      level = 0
      return this.renderDetailsTAbleByFee()
    } else {
      dataSource.forEach(item => {
        let newItem
        let myItem = item
        let fullname = get(item, 'feeTypeId.fullname')
        let fullnameList = fullname.split('/').length > 1 ? fullname.split('/') : fullname.split(i18n.get('／'))
        fullnameList.length = fullnameList.length < level ? fullnameList.length : level
        fullname = fullnameList.join('／') // @i18n-ignore
        newItem = this.getNewItemByName(feeTypes, fullname, level) || {}
        myItem.newFeetype = item.feeTypeId
        groups = this.addGroupsItem(newItem.id, myItem, groups)
      })
      return groups
    }
  }

  sortGroups = groups => {
    const { selectedSortMenu } = this.props
    const groupsArray = []
    Object.values(cloneDeep(groups)).forEach(item => {
      groupsArray.push(item)
    })
    if (selectedSortMenu.type === sortTypeMap.INPUT) {
      return groupsArray
    } else if (selectedSortMenu.type === sortTypeMap.AMOUNT.ASC) {
      return groupsArray.sort((p, n) => {
        return Number(p.map(line => line.feeTypeForm.amount.standard).reduce((a, b) => Number(a) + Number(b)) || 0) <
          Number(n.map(line => line.feeTypeForm.amount.standard).reduce((a, b) => Number(a) + Number(b)) || 0)
          ? -1
          : 1
      })
    } else if (selectedSortMenu.type === sortTypeMap.AMOUNT.DESC) {
      return groupsArray.sort((p, n) => {
        return Number(p.map(line => line.feeTypeForm.amount.standard).reduce((a, b) => Number(a) + Number(b)) || 0) <
          Number(n.map(line => line.feeTypeForm.amount.standard).reduce((a, b) => Number(a) + Number(b)) || 0)
          ? 1
          : -1
      })
    } else {
      return this.props.onSortGroupsChange(groupsArray, selectedSortMenu.type, '[0].feeTypeForm')
    }
  }

  getNewItemByName = (feeTypes, fullname) => {
    feeTypes.forEach(item => {
      if (!!~fullname.indexOf(item.name) && fullname !== item.name) {
        let nameList = fullname.split('／') // @i18n-ignore
        nameList.shift()
        let newname = nameList.join('／') // @i18n-ignore
        this.getNewItemByName(item.children, newname)
      } else if (!!~fullname.indexOf(item.name) && fullname === item.name) {
        itemByname = item
      }
    })
    return itemByname
  }
  addGroupsItem = (itemId, item, groups) => {
    if (groups[itemId]) {
      groups[itemId].push(item)
    } else {
      groups[itemId] = []
      groups[itemId].push(item)
    }
    return groups
  }
  renderDetailsTAble = () => {
    const {
      hiddenFields,
      onLineClick,
      onAddDetailClick,
      isEdit,
      type,
      selectAble,
      external,
      isForbid,
      field,
      handleChange,
      specificationComponents,
      bus,
      showType,
      currentNode,
      billSpecification,
      onChange
    } = this.props
    const { selectedData, invoiceImageMap, isInvoiceManagePermissions, allTemplate } = this.state
    let groups = {}
    if (showType === 'TABLE_NO_GROUP') {
      //表格汇总（不分组）
      return (
        <DetailsEditTable
          isEdit={isEdit}
          onLineClick={onLineClick}
          handleChange={handleChange}
          bus={bus}
          currentNode={currentNode}
          billSpecification={billSpecification}
          onChange={onChange}
        />
      )
    }
    let level = showType.charAt(showType.length - 1)
    groups = this.renderDetailsTableKind(level)
    const groupsArray = this.sortGroups(groups)
    const result = []
    for (let index = 0; index < groupsArray.length; index++) {
      const group = groupsArray[index]
      const key = group[0]?.feeTypeId?.id
      const feeSelectAble = !!~this.fnGetVisibleIds().indexOf(key)
      const ValidExternal = this.fnGetValidExternaForTable(external, group)
      result.push(
        <DetailsTable
          key={key}
          type={type}
          rowKey={r => r.idx}
          rowClassName={record => {
            const riskWarning = get(external, get(record, 'feeTypeForm.detailId'))
            const risknum = getRiskFieldNum()(riskWarning)
            if (risknum) {
              return isForbid ? 'row-wrapper row-err-wrapper' : 'row-wrapper row-warning-wrapper'
            }
            return 'row-wrapper'
          }}
          showType={showType}
          selectedData={selectedData}
          isEdit={isEdit}
          selectAble={selectAble}
          dataSource={group}
          allTemplate={allTemplate}
          feeSelectAble={feeSelectAble}
          selectChange={this.onSelectChange}
          onLineClick={onLineClick}
          handleAdd={onAddDetailClick}
          external={ValidExternal}
          isForbid={isForbid}
          invoiceImageMap={invoiceImageMap}
          onChange={this.handleOnChange}
          isInvoiceManagePermissions={isInvoiceManagePermissions}
          specificationComponents={specificationComponents}
          showDetailNo={true}
        />
      )
    }

    return result
  }

  handleOnChange = (isDeductible, id) => {
    //可抵扣税额
    const { fnUpdateBill, isInHistory } = this.props
    if (isInHistory) return
    const params = { isDeductible, id }
    api.invokeService('@bills:invoice-isDeductible', params).then(res => {
      fnUpdateBill && fnUpdateBill()
    })
  }

  getOwnerById(data = [], id = '') {
    return data.find(line => {
      return line.id === id
    })
  }

  fnGetOwnerInfo = ({ line, submitterId }) => {
    const { staffs } = this.props
    const ownerId = get(line, 'feeTypeForm.ownerId', '')
    const isOwn = ownerId?.id === submitterId?.id
    const ownerName = ownerId?.name ?? ''
    return { isOwn, ownerName }
  }

  fnGetSharedStaffsName = sharedStaffs => {
    const { staffs } = this.props
    let name
    let sharedIds = []
    const list = staffs.forEach(item => {
      if (sharedStaffs.includes(item.id)) {
        sharedIds.push(item.id)
        if (!name) {
          name = item.name
        }
      }
    })
    const sharedName = sharedIds.length === 1 ? `已共享给 ${name}` : `已共享给 ${name} 等${sharedIds.length}人`
    return { sharedName, sharedIds }
  }

  renderDetailsList = () => {
    const {
      dataSource = [],
      submitterId = {},
      selectAble,
      tagDataSource,
      onLineClick,
      onDelDetailClick,
      baseDataProperties,
      selectedData,
      isEdit,
      isModify,
      corporationList,
      external,
      riskInfo,
      field,
      isForbid,
      RiskPromptOptimization,
      isRecordExpends,
      showAllFeeType,
      apportionVisibleList,
      billState,
      showPayPlan,
      fnShareAction,
      isCopyDetail,
      isRemuneration,
      inRepaymentModal,
      billSpecification,
      multiplePayeesMode,
      autoExpenseWithBillStriction,
      type,
      configs,
      allowSelectionReceivingCurrency
    } = this.props
    const { isInvoiceManagePermissions, allTemplate, hiddenFieldsMap } = this.state
    const isPermitForm = tagDataSource?.openFrom === 'permit-form'
    const isEBussCard = configs?.find(item => item.isEBussCard)?.isEBussCard
    const EBussCardCanDelete = (isEBussCard && billState === 'new') || !isEBussCard

    const _this = this
    return dataSource.map((line, i) => {
      const feeSelectAble = isEdit || !!~this.fnGetVisibleIds().indexOf(line.feeTypeId.id)
      const ValidExternal = this.fnGetValidExternaForItem(external, line)
      const riskInfoDetail = this.fnGetValidExternaForItem(riskInfo, line)
      const detailId = get(line, 'feeTypeForm.detailId')
      const { isOwn, ownerName } = this.fnGetOwnerInfo({ line, submitterId })
      const hiddenFields = getHiddenFields(hiddenFieldsMap, line)

      // 待提交单据重新判断行程是否可编辑
      const trip = get(line, `feeTypeForm['u_行程规划'][0]`) // @i18n-ignore
      let cEdit = isEdit
      let cSelectAble = selectAble
      if (isEdit && trip) {
        cEdit = getTripEditable(trip, this.props.travelBlackList)
        cSelectAble = isEdit
      }
      const sharedStaffs = line?.sharedStaffs
      let sharedIds,
        sharedName = ''
      if (sharedStaffs) {
        const data = this.fnGetSharedStaffsName(sharedStaffs)
        sharedIds = data.sharedIds
        sharedName = data.sharedName
      }
      const allowDeleteFeeDetail = billSpecification?.configs?.find(v => v.ability === 'apply')
        ?.autoAssociateOrderConfig?.allowDeleteFeeDetail

      const canSelectFee = inRepaymentModal
        ? line.state === 'REEXCHANGE'
        : feeSelectAble && !(line?.feeTypeForm?.systemGenerationDetail && allowDeleteFeeDetail === false)

      let nextDeleteParams = null
      const handleConfirmDelete = () => {
        if (nextDeleteParams) {
          onDelDetailClick(nextDeleteParams)
          nextDeleteParams = null
          message.success(i18n.get('删除成功'))
        }
      }
      const popConfirmButton = <Popconfirm
        title={i18n.get('确定删除选中的费用明细吗')}
        onConfirm={handleConfirmDelete}
        onCancel={noop}
        okText={i18n.get('删除')}
        okButtonProps={{ theme: 'danger' }}
        content={i18n.get('删除后将不可恢复')}
        cancelText={i18n.get('取消')}
      >
        <OutlinedEditDeleteTrash fontSize={14} />
      </Popconfirm>

      return (
        <ActiveWrapper
          key={i}
          data={line}
          isEditable={
            isEdit &&
            !isRemuneration &&
            !(line?.feeTypeForm?.systemGenerationDetail && allowDeleteFeeDetail === false) &&
            EBussCardCanDelete &&
            !(type === 'expense' && autoExpenseWithBillStriction)
          }
          onRemove={(params) => nextDeleteParams = params}
          feeTypeForm={line?.feeTypeForm}
          deleteIcon={popConfirmButton}
        >
          <DetailsItem
            hiddenFields={hiddenFields}
            submitterId={submitterId}
            selectAble={isPermitForm ? false : selectAble && !isRemuneration}
            isCopyDetail={isCopyDetail}
            feeSelectAble={canSelectFee}
            selectedData={selectedData}
            field={field}
            billState={billState}
            dataSource={line}
            allTemplate={allTemplate}
            showPayPlan={showPayPlan}
            baseDataProperties={baseDataProperties}
            isShowBorder={i !== 0}
            onLineClick={onLineClick}
            index={i}
            isEditable={cEdit}
            isModify={isModify}
            detailId={detailId}
            onChangeChecked={this.onChangeChecked}
            corporationList={corporationList}
            external={ValidExternal}
            isForbid={isForbid}
            riskInfo={riskInfoDetail}
            RiskPromptOptimization={RiskPromptOptimization}
            isRecordExpends={isRecordExpends}
            sharedStaffs={sharedStaffs}
            onChange={this.handleOnChange}
            isInvoiceManagePermissions={isInvoiceManagePermissions}
            showAllFeeType={showAllFeeType}
            apportionVisibleList={apportionVisibleList}
            isOwn={isOwn}
            ownerName={ownerName}
            fnShareAction={fnShareAction}
            nodeId={line.id}
            sharedName={sharedName}
            sharedIds={sharedIds}
            multiplePayeesMode={multiplePayeesMode}
            autoExpenseWithBillStriction={autoExpenseWithBillStriction}
            allowSelectionReceivingCurrency={allowSelectionReceivingCurrency}
          />
        </ActiveWrapper>
      )
    })
  }

  getTemplate = props => {
    const { dataSource } = props
    let dataLinkEdits = null
    this.getAllHiddenFields(dataSource)
    if (dataSource) {
      for (const data of dataSource) {
        dataLinkEdits = data.specificationId?.components?.find(item => item.type === 'dataLinkEdits')
      }
    }
    if (dataLinkEdits && dataLinkEdits.showInDetails) {
      this.setState({ hasDataLinkEdits: true })
      api
        .invokeService('@bills:get:getDataLinkEditTemplate', {
          id: dataLinkEdits.referenceData.id,
          // type: behaviour === 'INSERT'
          type: true
        })
        .then(res => {
          this.setState({
            allTemplate: res.items
          })
        })
    }
  }

  getAllHiddenFields = async dataSource => {
    const [changed, hiddenFieldsMap] = await getDetailSpeHiddenFields(dataSource, this.state.hiddenFieldsMap)
    if (changed) {
      this.setState({ hiddenFieldsMap })
    }
  }

  render() {
    const { showType, isFullscreen } = this.props
    const { allTemplate, hasDataLinkEdits } = this.state
    const cls = classnames(styles['details-wrapper'], {
      [styles['fullscreen-scroll']]: isFullscreen
    })
    return (
      <div className={cls} ref={this.detailRef}>
        {showType.startsWith('TABLE')
          ? hasDataLinkEdits
            ? allTemplate && this.renderDetailsTAble()
            : this.renderDetailsTAble()
          : this.renderDetailsList()}
      </div>
    )
  }
}

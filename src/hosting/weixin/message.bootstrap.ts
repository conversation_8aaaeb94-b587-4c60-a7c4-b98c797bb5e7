/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/4/10 下午2:01.
 */

import { addNcpcCode } from '@ekuaibao/lib/lib/addNcpcCode'
import { app, callback } from '../index.v0'
import QYWeiXinSDK from '@ekuaibao/sdk-bridge/sdk/qyweixin'
import registerAPI from '../registerAPI'
import { localCacheSet } from '@ekuaibao/session-info'
import registerHandler from '../registerHandler'
import initializeTitle from '../initializeTitle'
import initPlatformInfo from '../initPlatformInfo'
import systemLimitLogout from '../systemLimitLogout'
import { initColors } from '../initColors'
/********注册平台类型*******/
window.__PLANTFORM__ = 'WEIXIN'
window.APPLICATION_VERSION = APPLICATION_VERSION

function redirect(): Promise<string> {
  const params = (() => {
    const arr = location.search.slice(1).split('&')
    const obj: any = {}
    for (let i = 0; i < arr.length; i++) {
      const arr2 = arr[i].split('=')
      obj[arr2[0]] = decodeURIComponent(arr2[1])
    }
    return obj
  })()
  return new Promise((resolve, reject) => {
    const billState = params.billState
    switch (billState) {
      case 'APPROVING':
      case 'SENDING':
      case 'RECEIVING':
      case 'PROCESSED':
        localCacheSet('beforePage', 'auditPending', true)
        resolve('/audit/approve')
        break
      case 'PAYING':
        localCacheSet('beforePage', 'auditPayment', true)
        resolve('/audit/payment')
        break
      case 'comment':
        localCacheSet('beforePage', 'auditCarbonCopy', true)
        resolve('/audit/carbonCopy')
        break
      default:
        reject(i18n.get('参数错误'))
    }
  })
}

function startup(hash: string) {
  app.use({
    id: '@weixin',
    onready() {
      addNcpcCode()
    }
  })

  app.useHistory({
    search: location.search.slice(1),
    initialEntries: [hash]
  })

  callback()
}

export const bootstrap = async () => {
  app.container.set('IDeviceType', 'DESKTOP')
  app.container.set('ISMessageEntry', true)
  app.sdk = app.container.get(QYWeiXinSDK)
  initColors()
  initPlatformInfo()
  systemLimitLogout()
  registerHandler()

  await (app.sdk as QYWeiXinSDK).initialize()
  window.isNewHome = true

  initializeTitle()

  registerAPI()
  const hash = await redirect()
  startup(hash)
}

import React, { PureComponent } from 'react'
import styles from './BillInfoModifyModal.module.less'
import BillInfoEditable from '../parts/right-part/billInfo/BillInfoEditable'
import RightPartHeader from '../parts/right-part/billInfo/RightPartHeader'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceModal } from '@ekuaibao/enhance-layer-manager'
import { EnhanceConnect } from '@ekuaibao/store'
import { setValidateError, getFeeDetailEditable } from '../bills.action'
import { fnFormatMoneyValue, getValidateErrorByShow, setFormMultiplePayeesMode } from '../util/billUtils'
import MessageCenter from '@ekuaibao/messagecenter'
import { Button } from '@hose/eui'
import { getV } from '@ekuaibao/lib/lib/help'
import { debounce, get } from 'lodash'
import { showMessage } from '@ekuaibao/show-util'
import { isFunction } from '@ekuaibao/helpers'
const { startLoadFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

@EnhanceConnect(
  state => ({
    userinfo: state['@common'].userinfo.data,
    CHANGJIEPay: state['@common'].powers.CHANGJIEPay,
    feeTypeVisibleObjForModify: state['@bills'].feeTypeVisibleObjForModify
  }),
  { setValidateError }
)
@EnhanceModal({
  footer: [],
  className: 'respond-modal-layer'
})
export default class BillInfoModifyModal extends PureComponent {
  state = {
    budgetMsg: '',
    budgetList: [],
    currentNode: {},
    feeDetailUneditable: false,
    disabled: true
  }

  bus = new MessageCenter()

  componentWillMount() {
    this.bus.on('footer:action:submit', this.handleSubmit)
    this.bus.on('savebtn:state:change', this.handleChangeButton)
    this.handleModifyFields(this.props)
    startLoadFlowPerformanceStatistics()
    api.invokeService('@remuneration:get:remuneration:config:setting')
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.dataSource !== nextProps.dataSource) {
      this.handleModifyFields(nextProps)
    }
  }

  async componentDidMount() {
    const flowId = getV(this, 'props.dataSource.id', '')
    const result = await getFeeDetailEditable({ flowId })
    this.setState({ feeDetailUneditable: result.value })
  }

  componentWillUnmount() {
    this.bus.un('footer:action:submit', this.handleSubmit)
    this.bus.un('savebtn:state:change', this.handleChangeButton)
  }

  handleChangeButton = debounce(({ disabled }) => {
    this.setState({ disabled })
  }, 200)

  handleCancel = () => {
    this.props.onCancel?.()
  }

  handleSubmit = debounce(() => {
    const { dataSource, setValidateError, backLogOwnerId } = this.props
    if (!isFunction(this.bus?.getValueWithValidate)) return
    this.bus.emit('handleBillInfoModifySave'); // 触发费用明细的表单校验
    this.bus
      .getValueWithValidate?.()
      .catch(e => {
        const components = get(dataSource, 'form.specificationId.components')
        const errors = getValidateErrorByShow(components, Object.keys(e))
        if (!errors.length) {
          setValidateError({ bill: Object.keys(e) })
        }
        return Promise.reject(e)
      })
      .then(formValue => {
        const hasDraftDetails = formValue?.details?.find(v => v?.feeTypeForm?.detailStatus === 'draft')
        if (hasDraftDetails) {
          showMessage.error(i18n.get('有草稿状态的明细，请先保存明细后再提交'))
          return 'failed'
        }
        setFormMultiplePayeesMode(formValue, dataSource)
        formValue = fnFormatMoneyValue({ data: formValue, specification: dataSource?.form?.specificationId })
        const optionalPayeeByZero = getV(dataSource, 'form.optionalPayeeByZero', false)
        formValue.optionalPayeeByZero = optionalPayeeByZero
        return this.bus.invoke('modify:bill:click', formValue, backLogOwnerId)
      })
      .then(state => {
        setValidateError({ bill: [], detail: [], trip: [] })
        state !== 'failed' && this.props.onOk()
      })
  }, 400)

  handleModifyFields(props) {
    const { dataSource } = props
    const { plan } = dataSource
    const { nodes, taskId, flowPlanConfigId } = plan
    const currentNode = nodes.find(node => node.id === taskId)
    this.setState({ currentNode: { ...currentNode, flowPlanConfigId } })
  }

  render4Draft() {
    const { dataSource, feeTypeVisibleObjForModify, riskData } = this.props
    const { currentNode, feeDetailUneditable } = this.state
    return (
      <div id="modify-bill-wrapper" className="modify-modal-content">
        <BillInfoEditable
          bus={this.bus}
          dataSource={dataSource}
          currentNode={currentNode}
          approveModify={true}
          isModify={true}
          feeTypeVisibleObjForModify={feeTypeVisibleObjForModify}
          feeDetailUneditable={feeDetailUneditable}
          riskData={riskData}
        />
      </div>
    )
  }

  renderFooter = () => {
    const { disabled } = this.state
    return (
      <div className="modal-footer">
        <Button className="btn-ml" category="secondary" onClick={this.handleCancel}>
          {i18n.get('取消')}
        </Button>
        <Button className="btn-ml" category="primary" onClick={this.handleSubmit} disabled={disabled}>
          {i18n.get('保存')}
        </Button>
      </div>
    )
  }

  render() {
    const { dataSource } = this.props

    return (
      <div id={'BillInfoModifyModal'} className={styles['bill-info-modify-modal']}>
        <RightPartHeader bus={this.bus} dataSource={dataSource} layer={this.props.layer} />
        {this.render4Draft()}
        {this.renderFooter()}
      </div>
    )
  }
}

import { app } from '@ekuaibao/whispered'
import { addNcpcCode } from '@ekuaibao/lib/lib/addNcpcCode'
import { callback } from '../index.v0'
import { newHomeConfig } from '../newHomeConfig'
import NbbankSdk from '@ekuaibao/sdk-bridge/sdk/nbbank'
import registerAPI from '../registerAPI'
import { initializeTitle } from '../initializeTitle'
import initPlatformInfo from '../initPlatformInfo'
import systemLimitLogout from '../systemLimitLogout'
import registerHandler from '../registerHandler'
import { fetchHandleError } from '../FetchConfig'
import { initRoute } from '../initRoute'
import { initColors } from '../initColors'
import initLogger from '../../logger/initLogger'
initLogger()
/********注册平台类型*******/
window.__PLANTFORM__ = 'NBBANK'

function startup() {
  app.use({
    id: '@nbbank',
    onready() {
      addNcpcCode()
    }
  })
  // app.use(require('../../plugins/account5').default)
  callback()
}

function modifierKey(e: any) {
  let modifier = 0
  if (e.metaKey) {
    modifier |= 1
  }
  if (e.ctrlKey) {
    modifier |= 2
  }
  if (e.altKey) {
    modifier |= 4
  }
  if (e.shiftKey) {
    modifier |= 8
  }
  return modifier
}

fetchHandleError()

window.addEventListener('keydown', function(e: KeyboardEvent) {
  const modifier = modifierKey(e)
  if (e.keyCode === 82 && (modifier === 1 || modifier === 2)) {
    // meta+r or ctrl+r
    window.location.reload()
  }
})

// 延迟处理，已确保jssdk执行完成
export const bootstrap = async () => {
  initColors()
  app.container.set('IDeviceType', 'DESKTOP')
  app.container.set('ISMessageEntry', false)
  app.sdk = app.container.get(NbbankSdk)

  registerHandler()

  await (app.sdk as NbbankSdk).initialize()
  initRoute()
  initPlatformInfo()
  systemLimitLogout()
  await newHomeConfig()
  initializeTitle()

  registerAPI()
  await startup()
}

if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'beta') {
  require('../development')
}

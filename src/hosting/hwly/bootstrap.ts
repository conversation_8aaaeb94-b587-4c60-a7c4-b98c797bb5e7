/*!
 * 华为联运对接
 * @since 2022-12-08 12:02:42
 */

import { app } from '@ekuaibao/whispered'
import { initDatafluxRum } from '../dataflux-rum'
import { addNcpcCode } from '@ekuaibao/lib/lib/addNcpcCode'
import { handleError, checkGroupAPPLogin, initUserInfo } from '../../lib/lib-util'
import { callback } from '../index.v0'
import { newHomeConfig } from '../newHomeConfig'
import { eKuaibaoSDK } from '@ekuaibao/sdk-bridge/sdk/ekuaibao'
import registerAPI from '../registerAPI'
import initializeTitle from '../initializeTitle'
import registerHandler from '../registerHandler'
import initPlatformInfo from '../initPlatformInfo'
import systemLimitLogout from '../systemLimitLogout'
import { fetchHandleError } from '../FetchConfig'
import { initRoute } from '../initRoute'
import { initColors } from '../initColors'
import { initSessionFromUrl } from '../initSessionFromUrl'
import setNetworkAdapter from '../setNetworkAdapter'
import qs from 'qs'
require('../../styles/app.less')
import initLogger from '../../logger/initLogger'

initLogger()
/********注册平台类型*******/
window.__PLANTFORM__ = 'HWLY'

initDatafluxRum();

// 集团版环境的地址
window.GROUP_URL =
  process.env.NODE_ENV === 'production'
    ? 'https://group.ekuaibao.com/web/group.html'
    : 'http://team-movie1.dev.ekuaibao.cn/web/group.html'

checkGroupAPPLogin()

function startup() {
  const params = qs.parse(location.search.slice(1))
  if (params?.billentry) {
    window.__SUB_PLANNTFORM__ = 'BILLENTRY'
  }
  app.use({
    id: '@app',
    onready() {
      addNcpcCode()
    }
  })
  callback()
}


fetchHandleError(handleError)

export async function bootstrap() {
  initColors()
  initSessionFromUrl()
  initRoute()
  await initPlatformInfo()
  if (window.PLATFORMINFO?.clusterURL) {
    await initUserInfo(true)
  }
  systemLimitLogout()
  app.container.set('IDeviceType', 'DESKTOP')
  app.container.set('ISMessageEntry', false)
  app.sdk = app.container.get(eKuaibaoSDK)

  registerHandler()

  startup()
  registerAPI()

  try {
    await (app.sdk as eKuaibaoSDK).initialize()
    setNetworkAdapter()
  } catch (e) {
    await newHomeConfig()
    setNetworkAdapter()
    return
  }
  await newHomeConfig()
  
  initializeTitle()
}


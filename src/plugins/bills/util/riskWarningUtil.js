import { app as api } from '@ekuaibao/whispered'
import { isErrorItem } from '../riskWarning/ai-audit-result/utils'
export function reorganizeRiskWarningData(msg) {
    const res = msg.reduce((result, current, index) => {
        Array.isArray(current.riskWarning) && current.riskWarning.map(v => {
            if (result[v + current.pathValueId]) {
                result[v + current.pathValueId].messages.push(`${current.invoiceMsg}`)
            } else {
                result[v + current.pathValueId] = {
                    controlName: v + "：",
                    type: 'invoice',
                    controlField: 'invoiceForm',
                    path: current.path, // 费用明细
                    pathValueId: current.pathValueId, // 费用明细id
                    isOutOfLimitReject: isErrorItem(current),
                    messages: [`${current.invoiceMsg}`],
                    isReview: false
                }
            }
        })
        /*
            reviewFlag
                第一期为是否显示发票复核未收到纸质发票风险提示语的标志：https://hose2019.feishu.cn/wiki/wikcnrUcoLRbXs0xgxRer1QHQXf
                第二期修改变成了是否配置了发票复核风险提示规范的标志：https://hose2019.feishu.cn/wiki/wikcnIEUkW29sh1CHoYrvw2aiog
        */
        if(current.reviewFlag){
            const ristText = i18n.get("未收到发票的纸质发票")
            window.IS_SZJL && current.riskWarning?.push(ristText)
            if (result['isReview' + current.pathValueId]) {
                result['isReview' + current.pathValueId].messages.push(`${current.invoiceMsg}`)
            } else {
                result['isReview' + current.pathValueId] = {
                    controlName: ristText + '：',
                    type: 'invoice',
                    controlField: 'invoiceForm',
                    path: current.path, // 费用明细
                    pathValueId: current.pathValueId, // 费用明细id
                    messages: [`${current.invoiceMsg}`],
                    isReview: true  // 复核风险
                }
            }
        }
        return result
    }, {})
    // 未收到发票的纸质发票排序到最后
    const arr = Object.values(res).sort((a,b) => {
        return Number(a.isReview) - Number(b.isReview)
    })
    return arr
}
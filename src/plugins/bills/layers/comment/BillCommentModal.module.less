@import '~@ekuaibao/web-theme-variables/styles/colors';

.bill-comment-modal {
  max-height: 600px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  :global {
    .modal-header {
      border-bottom: none;
      height: 60px;
      font-size: 20px;
      .cross-icon {
        font-size: 16px;
      }
    }
    .mention {
      width: 100%;
      height: 205px;
    }
    .comment {
      min-height: 284px;
      padding: 16px 32px;
      overflow-y: auto;
      >form {
        position: relative;
        z-index: 200;
      }
      .comment-tool {
        margin-top: 8px;
        display: flex;
        flex-direction: row;
        position: relative;
        .attachment-style {
          width: 100%;
          .ant-btn {

            border: none;
            margin-left: -15px;
            background-color: #ffffff;
          }
          .ekb-files-uploader-wrapper {
            .ekb-files-input {
              font-size: 0; // fix IE10 光标闪烁
              width: 80px;
            }
          }
          .gpy-upload-clipboard-wrapper {
            left: 142px;
            top: 0;
          }
          .gpy-upload-en-clipboard-wrapper {
            left: 196px;
            top: 0;
          }
        }
        .comment-remind {
          width: 85px;
          height: 28px;
          position: absolute;
          left: 85px;
          top: 1px;
          cursor: default;
          display: flex;
          align-items: center;
          .img {
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }
        }
      }
      .ant-form-item {
        margin-bottom: 0;
      }
    }

    .modal-footer {
      flex-shrink: 0;
    }

    .modal-footer-button {
      display: flex;
      flex-direction: row;
      padding: 12px 24px;
      height: 56px;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 4px 24px 0 rgba(29, 43, 61, 0.2);
      .btn-ml {
        margin-right: 8px;
      }
    }
  }
}

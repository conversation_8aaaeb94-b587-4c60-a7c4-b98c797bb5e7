import { app } from '@ekuaibao/whispered'
import { addNcpcCode } from '@ekuaibao/lib/lib/addNcpcCode'
import { callback } from '../index.v0'
import { newHomeConfig } from '../newHomeConfig'
import { HybridSDK } from '@ekuaibao/sdk-bridge/sdk/hybrid'
import registerAPI from '../registerAPI'
import registerHandler from '../registerHandler'
import initPlatformInfo from '../initPlatformInfo'
import { initRoute } from '../initRoute'
import { initColors } from '../initColors'
import setNetworkAdapter from '../setNetworkAdapter'
import { fetchHandleError } from '../FetchConfig'
import { handleError } from './util'
import initializeTitle from '../initializeTitle'
require('../../styles/app.less')
import initLogger from '../../logger/initLogger'
import { initDatafluxRum } from '../dataflux-rum'

initDatafluxRum();
initLogger()
/********注册平台类型*******/
window.__PLANTFORM__ = 'HYBRID'

function startup() {
  app.use({
    id: '@app',
    onready() {
      addNcpcCode()
    }
  })
  callback()
}
fetchHandleError(handleError)

export async function bootstrap() {
  initColors()
  initRoute()
  initPlatformInfo()
  // systemLimitLogout()
  app.container.set('IDeviceType', 'DESKTOP')
  app.container.set('ISMessageEntry', false)
  app.sdk = app.container.get(HybridSDK)
  registerHandler()

  startup()
  registerAPI()

  try {
    await (app.sdk as HybridSDK).initialize()
    setNetworkAdapter()
  } catch (e) {
    await newHomeConfig()
    setNetworkAdapter()
    return
  }
  await newHomeConfig()
  initializeTitle()
}

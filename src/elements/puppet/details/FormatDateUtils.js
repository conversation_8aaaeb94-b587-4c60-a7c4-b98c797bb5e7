import React from 'react'
import moment from 'moment'
import CardListView from '../../../elements/InvoiceCard/InvoiceItem'
import { parseAsShowValue } from '../../../elements/invoice-form/utils/config'
import { app as api } from '@ekuaibao/whispered'
import { IMG_REG } from '@ekuaibao/lib/lib/enums'
import { formatDateTime } from '../../../components/utils/fnPredefine4Date'
import { QuerySelect } from 'ekbc-query-builder'

function buildFeeDate2String(feeDate, isPeriod = false, components = []) {
  if (!feeDate) {
    return ''
  }
  const dateRangeComponent = components.find(
    component => component.type === 'dateRange' && component.field === 'feeDatePeriod'
  )
  const feeDateComponent = components.find(component => component.type === 'date' && component.field === 'feeDate')
  const dataComponent = dateRangeComponent || feeDateComponent
  if (!dataComponent) {
    return ''
  }
  const { dateTimeType, withTime } = dataComponent
  const dateFormat = formatDateTime(withTime, dateTimeType)
  if (isPeriod && typeof feeDate === 'object') {
    let { start, end } = feeDate
    start = moment
      .utc(start)
      .utcOffset(8)
      .format(dateFormat)
    end = moment
      .utc(end)
      .utcOffset(8)
      .format(dateFormat)
    return `${start} – ${end}`
  }
  return moment
    .utc(parseInt(feeDate))
    .utcOffset(8)
    .format(dateFormat)
}

export function fnGetDetailsByType(feeTypeForm, components) {
  let { feeDate, feeDatePeriod, fromCity, toCity, city } = feeTypeForm
  let feeDateString = ''
  let cityStr = ''
  if (feeDatePeriod) {
    feeDateString = buildFeeDate2String(feeDatePeriod, true, components)
  } else if (feeDate && !feeDatePeriod) {
    feeDateString = buildFeeDate2String(feeDate, false, components)
  } else {
    feeDateString = i18n.get('无消费日期')
  }

  try {
    const isUS = i18n.currentLocale === 'en-US'
    if (city) {
      const cityObj = JSON.parse(city)[0]
      cityStr = `${isUS && cityObj?.enLabel ? cityObj?.enLabel : cityObj?.label}`
    }
    if (fromCity && toCity) {
      const fromCityObj = JSON.parse(fromCity)[0]
      const toCityObj = JSON.parse(toCity)[0]
      cityStr = `${isUS && fromCityObj?.enLabel ? fromCityObj?.enLabel : fromCityObj?.label}-${isUS && toCityObj?.enLabel ? toCityObj?.enLabel : toCityObj?.label}`
    }
  } catch (error) { }

  return { cityStr, feeDateString }
}

export function renderTitle(invoiceForm) {
  if (!invoiceForm) return ''
  return invoiceForm.attachments || invoiceForm.fileList ? '' : invoiceForm.invoices ? '' : i18n.get('开票方')
}

export function renderStyle(invoiceForm) {
  if (!invoiceForm) return { display: 'none' }
  return Object.keys(invoiceForm).length > 1 &&
    (invoiceForm.type === 'unify' ||
      (invoiceForm.type === 'exist' &&
        ((invoiceForm.attachments && invoiceForm.attachments.length) ||
          (invoiceForm.invoices && invoiceForm.invoices.length))) ||
      ((invoiceForm.type === 'noWrite' || invoiceForm.type === 'exist') &&
        invoiceForm.invoices &&
        invoiceForm.invoices.length))
    ? { maxWidth: 512 }
    : { display: 'none' }
}

function handleFilePreview(line) {
  if (!IMG_REG.test(line.fileName)) {
    api.emit('@vendor:preview:byFetch', line.url, line.fileName, line.key || line.fileName)
  } else {
    api.emit('@vendor:preview:images', [line], line)
  }
}

export function renderContent(
  invoiceForm,
  submitterId,
  invoiceImageList,
  isInvoiceManagePermissions,
  onChange,
  billState = ''
) {
  if (!invoiceForm) return ''
  if (invoiceForm.attachments || invoiceForm.invoices) {
    const invoiceDetailList = invoiceForm.invoices || []
    const fileList = invoiceForm.attachments
    const showValue = parseAsShowValue(fileList, invoiceDetailList)
    return (
      <div style={renderStyle(invoiceForm)}>
        <CardListView
          dataSource={showValue || []}
          isEdit={false}
          isHover={true}
          billState={billState}
          submitterId={submitterId}
          isDownload={false}
          invoiceNum={showValue.length} //只有明细列表和明细表格发票张数为1时，展示发票所有的明细
          invoiceImgList={invoiceImageList}
          onFilePreview={handleFilePreview}
          handleImgPreview={handleFilePreview}
          onChange={onChange} //可抵扣税额开关
          isInvoiceManagePermissions={isInvoiceManagePermissions}
          isEditAuthenticity={false} //验证发票
        />
      </div>
    )
  } else if (invoiceForm.invoiceCorporation) {
    return invoiceForm.invoiceCorporation.name
  }
}

export const invoiceFormMap = () => ({
  exist: i18n.get('已有发票'),
  noExist: i18n.get('无发票'),
  wait: i18n.get('待开发票'),
  unify: i18n.get('统一开票'),
  noWrite: i18n.get('无需填写'),
  existConfirm: i18n.get('已有发票(待确认)')
})

export const GetInvoiceStateText = ({invoiceFormState, invoiceForm}) => {
  const [invoiceBatchLength, setInvoiceBatchLength] = React.useState(0)
  React.useEffect(() => {
    if (invoiceFormState === 'exist' && invoiceForm?.supplementInvoiceBatchId) {
      // 获取批次里面的发票
      getSupplementInvoiceBatch(invoiceForm.supplementInvoiceBatchId).then(res => {
        setInvoiceBatchLength(res?.length ?? 0)
      })
    }
  }, [invoiceForm])
  if (invoiceFormState === 'exist') {
    const invoiceLength = invoiceForm ? parseAsShowValue(invoiceForm.attachments, invoiceForm.invoices || []).length : 0
    return `${invoiceFormMap()[invoiceFormState]}*${invoiceLength + invoiceBatchLength}`
  } else {
    return invoiceFormMap()[invoiceFormState] || null
  }
}
const getSupplementInvoiceBatch = async supplementInvoiceBatchId => {
  const query = new QuerySelect().filterBy(
    `batchNo.containsIgnoreCase("${supplementInvoiceBatchId}")||id.containsIgnoreCase("${supplementInvoiceBatchId}")`
  )
  const res = await api.invokeService('@invoice-manage:search:WaitingBatchInvoice', query.value())
  const waitingBatchInvoice = res?.items[0]
  return waitingBatchInvoice?.invoices ?? []
}
export const getInvoiceTagColorByType = type => {
  switch (type) {
    case 'exist':
    case 'existConfirm':
    case 'unify':
      return 'success'
    case 'wait':
      return 'warning'
    case 'noExist':
    case 'noWrite':
      return 'default'
    default:
      return 'default'
  }
}

const getActionValue = (actionObj, fId, sId) => {
  const { isActive, isChange, isLast, feeTypeId, specificationId } = actionObj
  if (fId !== feeTypeId) {
    return ''
  } else {
    if (sId === specificationId) {
      if (!isActive) {
        return i18n.get('已停用')
      }
      if (!isLast) {
        return i18n.get('非末级')
      }
      if (isChange) {
        return i18n.get('版本变更')
      }
    } else {
      if (!isActive) {
        return i18n.get('已停用')
      }
      if (!isLast) {
        return i18n.get('非末级')
      }
    }
  }
}

export const formatFeeTypeList = (dataSourceList = [], feeChangeInfo, addKey) => {
  if (!dataSourceList.length) return dataSourceList
  return dataSourceList.map((item, index) => {
    const { feeTypeId = {}, specificationId = {} } = item
    const actionObj = feeChangeInfo[`${feeTypeId.id}${index}`]
    if (actionObj) {
      feeTypeId.actionValue = getActionValue(actionObj, feeTypeId.id, specificationId.id)
    }
    item.feeTypeId = feeTypeId
    if (addKey) {
      item.key = item.idx
    }
    return item
  })
}

export const sortType = {
  INPUT: 'INPUT',
  AMOUNT: 'AMOUNT',
  DATE: 'DATE',
  ASC: 'ASC',
  DESC: 'DESC'
}

export const sortTypeMap = {
  INPUT: 'INPUT', // 默认输入顺序
  AMOUNT: {
    ASC: 'AMOUNT_ASC', // 费用金额正序
    DESC: 'AMOUNT_DESC' // 费用金额倒序
  },
  DATE: {
    ASC: 'ASC', // 日期正序
    DESC: 'DESC' // 日期倒序
  }
}

const subSortType = {
  ASC: i18n.get('正序'),
  DESC: i18n.get('倒序')
}

// 费用明细排序子级数据格式化
export const formateSortTypeMenu = (type, field = '', fieldName = '') => {
  return Object.keys(subSortType).map(key => {
    const item = {}
    const name = subSortType[key]
    item.label = name
    item.name = i18n.get(`${fieldName}${name}`)
    item.type = type === sortType.AMOUNT ? sortTypeMap[type][key] : `${sortTypeMap[type][key]}_${field}`
    return item
  })
}

export const sortTypeList = [
  { type: sortType.INPUT, label: i18n.get('默认排序') },
  {
    type: sortType.AMOUNT,
    label: i18n.get('费用金额'),
    children: formateSortTypeMenu(sortType.AMOUNT, 'amount', '费用金额')
  }
]

export default {
  fnGetDetailsByType,
  renderTitle,
  renderContent,
  invoiceFormMap,
  formatFeeTypeList,
  sortTypeList,
  sortType
}

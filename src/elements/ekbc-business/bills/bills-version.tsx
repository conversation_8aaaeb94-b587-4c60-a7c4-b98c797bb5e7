import React, { FC, useEffect, useState } from 'react'
import { app as api } from '@ekuaibao/whispered'
import { Button } from '@hose/eui'
import { getBillHistoryVersionList } from '../../../plugins/bills/bills.action'

const BillsVersion: FC<{ logs: any[]; flowId: string; privilegeId?: string }> = ({ flowId, logs, privilegeId }) => {
  const [versionList, setVersionList] = useState([])
  const versionConfig = api.getState('@common.specificationVersionConfig')
  if (versionConfig && versionConfig.hideVersion) {
    return null
  }

  useEffect(() => {
    if (logs?.length){
      getBillHistoryVersionList(flowId, privilegeId).then(res => {
        if (res.items) {
          setVersionList(res.items)
        }
      })
    }
  }, [logs])

  const handleHistoryVersionClick = () => {
    api.open('@bills:BillHistoryVersionModal', { flowId, item: { flowVersionedId: versionList[0]?.id } })
  }

  const HiddenFlowHistory = api.getState()['@common'].powers.HiddenFlowHistory
  
  if (versionList?.length < 1 || HiddenFlowHistory) return <></>

  return (
    <Button category="secondary" onClick={handleHistoryVersionClick}>
      {i18n.get('查看历史版本')}{' '}
    </Button>
  )
}

export default BillsVersion

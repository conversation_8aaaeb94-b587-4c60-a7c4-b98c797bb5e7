import React, { PureComponent } from 'react'
import styles from './DetailsTable.module.less'
import { createColumnsForEUITable, buildSelectedKeys, polyfillDataIndexForEUI } from './tableUtil'
import TableWrapper from './TableWrapper'
import TableHeader from './TableHeader'
import Loading from '@ekuaibao/loading'
import { EnhanceConnect } from '@ekuaibao/store'
import { getV } from '@ekuaibao/lib/lib/help'
import { cloneDeep, flatten, get, some } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import Money from '../../elements/puppet/Money'
import { hasForeign, hasBudget } from '../../components/utils/fnCurrencyObj'
import { NullCell } from './columnRender'
import { FilledTipsWarning } from '@hose/eui-icons'
import { flattenObject } from '../../plugins/bills/riskWarning/utils'
import { calcDetailsAmount, calcDetailsReceivingAmount } from '@ekuaibao/lib/lib/lib-util'
import { isErrorItem } from '../../plugins/bills/riskWarning/ai-audit-result/utils'

@EnhanceConnect(state => {
  return {
    baseDataProperties: state['@common'].globalFields.data,
    multiplePayeesMode: state['@bills'].multiplePayeesMode,
    feeTypes: state['@common'].feetypes.data
  }
})
export default class DetailsTable extends PureComponent {
  constructor(props) {
    super(props)
    const { selectedData, dataSource } = props
    const selectedRowKeys = buildSelectedKeys(selectedData, dataSource)
    this.state = {
      selectedRowKeys,
      components: []
    }
  }

  componentWillMount() {
    const { dataSource, type, specificationComponents, multiplePayeesMode, hiddenFieldsMap } = this.props
    const {
      feeTypeId,
      specificationId: { components: coms }
    } = dataSource[0]
    const specificationId =
      feeTypeId[`${type === 'permit' ? 'requisition' : type}SpecificationId`] || feeTypeId['expenseSpecificationId']
    api.invokeService('@bills:import:getFeeTypeTemplateById', specificationId).then(action => {
      if (action.error) {
        return this.setState({ components: coms })
      }
      const specification = get(action, 'payload.value') || get(action, 'value')
      const { components } = specification
      if (multiplePayeesMode) {
        const payeeId = cloneDeep(specificationComponents.find(item => item?.field === 'payeeId'))
        if (payeeId) {
          payeeId.field = 'feeDetailPayeeId'
          components.push(payeeId)
        }
      }
      this.setState({ components })
    })
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.selectedData !== nextProps.selectedData) {
      const { selectedData, dataSource } = nextProps
      this.setState({
        selectedRowKeys: buildSelectedKeys(selectedData, dataSource)
      })
    }
  }

  handleTableRowClick = (item, index, event) => {
    const { onLineClick } = this.props
    onLineClick && onLineClick(item, index)
  }

  onChange = selectedRowKeys => {
    // console.info(selectedRowKeys)
    // this.setState({ selectedRowKeys })
  }

  onSelect = (record, selected, selectedRows) => {
    const { selectChange } = this.props
    selectChange && selectChange([record], selected)
  }

  onSelectAll = (selected, selectedRows, changeRows) => {
    const { selectChange } = this.props
    selectChange && selectChange(changeRows, selected)
  }

  renderForeignMoney = (money, record) => {
    const foreignStrCode = getV(money, 'foreignStrCode', '')
    const standardStrCode = getV(money, 'standardStrCode', '')
    const receiving = getV(record, 'feeTypeForm.receivingAmount', '')
    const receivingForeignStrCode = getV(receiving, 'foreignStrCode', '')
    const isShowForeign = foreignStrCode || receivingForeignStrCode ? true : false
    // 金额没有外币时，收款金额外币则展示
    if (receiving && !isShowForeign) {
      money = { ...money, ...(receiving || {}) }
    }
    let strCode = foreignStrCode ? foreignStrCode : standardStrCode
    if (isShowForeign) {
      return (
        <div className={styles['money-renderer-wrapper']} style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', fontSize: '14px' }}>
          <span style={{ minWidth: '30px' }}>{strCode}</span>
          <Money
            value={money}
            withoutStyle={true}
            showSymbol={false}
            isShowForeign={isShowForeign}
            showForeignNum={false}
          />
        </div>
      )
    }
    return <NullCell style={{ display: 'inline-block', textAlign: 'right', width:'100%' }}/>
  }

  render() {
    const { components } = this.state
    if (!components.length) return <Loading className="dis-f jc-c flex-1 center" color="var(--brand-base)" />
    const {
      dataSource,
      feeSelectAble,
      handleAdd,
      isEdit,
      selectAble,
      external,
      isForbid,
      invoiceImageMap,
      isInvoiceManagePermissions,
      onChange,
      showType,
      allTemplate,
      showDetailNo,
      ...others
    } = this.props
    let { selectedRowKeys } = this.state
    const amount = calcDetailsAmount(dataSource)
    const receivingAmountTotal = calcDetailsReceivingAmount(dataSource)
    let columns = createColumnsForEUITable(
      components,
      dataSource,
      external,
      isForbid,
      invoiceImageMap,
      isInvoiceManagePermissions,
      onChange,
      allTemplate
    )
    const currency = api.getState('@common').powers.Currency
    const moneyFieldIndex = columns.findIndex(item => item.type === 'money' && polyfillDataIndexForEUI(item.dataIndex) === 'feeTypeForm.amount')
    const amountField = columns[moneyFieldIndex]
    let isShowForeignMoney = !!amountField ? some(dataSource, el => hasForeign(get(el, 'feeTypeForm.amount'))) : false
    let isShowBudgetMoney = !!amountField ? some(dataSource, el => hasBudget(get(el, 'feeTypeForm.amount'))) : false
    let hasReceivingAmount = !!amountField
      ? dataSource.find(el => get(el, 'feeTypeForm.receivingAmount'))?.feeTypeForm?.receivingAmount
      : false
    let hasDiffForeign = false
    let hasDiffStandard = false
    let amountTitle = i18n.get('本位币') + amountField.label

    if (currency) {
      const { dataIndex, label: title } = amountField
      if (isShowBudgetMoney) {
        columns.splice(moneyFieldIndex + 1, 0, {
          title: <div style={{ textAlign: 'right' }}>{i18n.get('预算币') + title}</div>,
          key: 'budgetMoney',
          dataIndex,
          width: 150,
          className: '',
          render: (money, record) => {
            if (money?.budgetStrCode) {
              return (
                <div className={styles['money-renderer-wrapper']} style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', fontSize: '14px' }}>
                  <span style={{ minWidth: '30px' }}>{money.budgetStrCode}</span>
                  <Money value={money?.budget} withoutStyle={true} showSymbol={false} showForeignNum={false} />
                </div>
              )
            }
            return <NullCell />
          }
        })
      }

      if (isShowForeignMoney) {
        columns.splice(moneyFieldIndex + 1, 0, {
          title: <div style={{ textAlign: 'right' }}>{i18n.get('原币') + title}</div>,
          key: 'foreignMoney',
          dataIndex,
          width: 150,
          className: '',
          render: this.renderForeignMoney
        })
      }

      const foreignColumnIndex = columns.findIndex(item => item.key === 'foreignMoney')
      if (hasReceivingAmount) {
        // 有不同的外币
        hasDiffForeign = some(
          dataSource,
          el =>
            get(el, 'feeTypeForm.receivingAmount.foreignStrCode') !== get(el, 'feeTypeForm.amount.foreignStrCode') &&
            get(el, 'feeTypeForm.receivingAmount.foreignStrCode') !== get(el, 'feeTypeForm.amount.standardStrCode')
        )
        // 有不同的本位币
        hasDiffStandard = some(
          dataSource,
          el => get(el, 'feeTypeForm.receivingAmount.standardStrCode') !== get(el, 'feeTypeForm.amount.standardStrCode')
        )

        if (hasDiffForeign && foreignColumnIndex < 0) {
          columns.splice(moneyFieldIndex + 1, 0, {
            title: <div style={{ textAlign: 'right' }}>{labelTitle}</div>,
            key: 'foreignMoney',
            dataIndex,
            width: 150,
            className: '',
            render: this.renderForeignMoney
          })
        }
        if (hasDiffStandard) {
          const insertIndex = foreignColumnIndex >= 0 ? foreignColumnIndex : moneyFieldIndex
          columns.splice(insertIndex + 1, 0, {
            title: <div style={{ textAlign: 'right' }}>{i18n.get('收款金额')}</div>,
            key: 'receivingAmount',
            dataIndex,
            width: 150,
            className: '',
            render: (_, record) => {
              const money = getV(record, 'feeTypeForm.receivingAmount','')
              const standardStrCode = getV(money, 'standardStrCode', '')
              if (money) {
                return (
                  <div className={styles['money-renderer-wrapper']} style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', fontSize: '14px' }}>
                    <span style={{ minWidth: '30px' }}>{standardStrCode}</span>
                    <Money
                      value={money}
                      withoutStyle={true}
                      showSymbol={false}
                      isShowForeign={false}
                      showForeignNum={false}
                    />
                  </div>
                )
              }
              return <NullCell />
            }
          })
        }
      }

      // 有收款币种并且不展示外币情况下
      amountTitle =
        !(hasDiffForeign && foreignColumnIndex < 0) && !isShowForeignMoney && hasDiffStandard
          ? i18n.get('原币') + amountField.label
          : amountTitle
      // 只有本位币的情况下 展示【金额】
      amountTitle = !hasDiffStandard && hasReceivingAmount ? amountField.label : amountTitle

      if (isShowBudgetMoney || isShowForeignMoney || hasDiffStandard || hasDiffForeign) {
        amountField.title = <div style={{ textAlign: 'right' }}>{amountTitle}</div>
      }
    }
    const isShowWarning = !!~dataSource.findIndex(record => {
      const riskWarning = get(external, get(record, 'feeTypeForm.detailId'), [])
      return Object.keys(riskWarning).length
    })
    if (isShowWarning) {
      columns.push({
        title: i18n.get('风险'),
        key: 'warning',
        width: 150,
        className: 'warning-cell-wrapper',
        render: (text, record) => {
          const riskWarning = get(external, get(record, 'feeTypeForm.detailId'))
          const flattenRisk = flattenObject(riskWarning)
          const risknum = flattenRisk.length
          const isForbid = some(flattenRisk, (item) => isErrorItem(item))
          if (risknum) {
            return (
              <span className={isForbid ? 'columns-wrapper-error' : 'columns-wrapper-warning'}>
                <FilledTipsWarning className={`icon`} />
                {risknum}
              </span>
            )
          }
          return <span className="columns-wrapper-normal">{isEdit ? <NullCell /> : i18n.get('无风险')}</span>
        }
      })
    }

    if (showDetailNo) {
      columns.unshift({
        title: i18n.get('序号'),
        key: 'detailNo',
        width: 50,
        className: 'warning-cell-wrapper',
        render: (_text, record) => {
          const detailNo = get(record, 'feeTypeForm.detailNo', '')
          return <span>{detailNo}</span>
        }
      })
    }

    if (selectAble) {
      // columns = addEmptyColumn(columns)
      others.rowSelection = {
        selectedRowKeys,
        // fixed: true,
        columnWidth: 30,
        onChange: this.onChange,
        onSelect: this.onSelect,
        onSelectAll: this.onSelectAll,
        getCheckboxProps: line => ({
          disabled: (!feeSelectAble && !isEdit) || !line.tripEdit
        })
      }
    }
    let feeTypeData
    let level = showType.charAt(showType.length - 1)
    if (level === 'E' || level === 'P') {
      feeTypeData = dataSource[0].feeTypeId
    } else {
      feeTypeData = cloneDeep(dataSource[0].newFeetype)
      let name = feeTypeData?.fullname
      let namelist = []
      if (name) {
        if (!!~name.indexOf('/')) {
          namelist = name && name.split(i18n.get('/'))
          namelist.length = level
          name = namelist.join(i18n.get('／'))
        } else if (!!~name.indexOf(i18n.get('／'))) {
          namelist = name && name.split(i18n.get('／'))
          namelist.length = level
          name = namelist.join(i18n.get('／'))
        }
        feeTypeData.fullname = name
      } else {
        feeTypeData.fullname = feeTypeData?.name
      }
    }
    return (
      <div className={styles['details-table-wrapper']}>
        <TableHeader
          showType={showType}
          feeSelectAble={feeSelectAble}
          feetype={feeTypeData}
          amount={receivingAmountTotal && hasReceivingAmount ? receivingAmountTotal : amount}
          handleAdd={handleAdd}
          newFeetype={dataSource[0].newFeetype}
        />
        <TableWrapper
          {...others}
          bordered={true}
          columns={columns}
          dataSource={dataSource}
          tableRowClick={this.handleTableRowClick}
          scroll={dataSource.length > 5 ? { y: 230 } : {}}
        />
      </div>
    )
  }
}

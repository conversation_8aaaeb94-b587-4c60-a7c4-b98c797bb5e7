import { addNcpcCode } from '@ekuaibao/lib/lib/addNcpcCode'
import { app, callback } from '../index.v0'
import { newHomeConfig } from '../newHomeConfig'
import { initDatafluxRum } from '../dataflux-rum'
import QYWeiXinSDK from '@ekuaibao/sdk-bridge/sdk/qyweixin'
import registerAPI from '../registerAPI'
import { initializeTitle } from '../initializeTitle'
import initPlatformInfo from '../initPlatformInfo'
import systemLimitLogout from '../systemLimitLogout'
import registerHandler from '../registerHandler'
import { initRoute } from '../initRoute'
import { initColors } from '../initColors'
import setNetworkAdapter from '../setNetworkAdapter'
import initLogger from '../../logger/initLogger'
initLogger()
/********注册平台类型*******/
window.__PLANTFORM__ = 'WEIXIN'

initDatafluxRum();

// window.UPLOAD_INVOICE_FILE_URL = UPLOAD_INVOICE_FILE_URL
function startup() {
  app.use({
    id: '@weixin',
    onready() {
      addNcpcCode()
    }
  })

  callback()
}

export const bootstrap = async () => {
  initColors()
  app.container.set('IDeviceType', 'DESKTOP')
  app.container.set('ISMessageEntry', false)
  app.sdk = app.container.get(QYWeiXinSDK)

  registerHandler()

  await (app.sdk as QYWeiXinSDK).initialize()
  initRoute()
  initPlatformInfo()
  systemLimitLogout()
  await newHomeConfig()

  initializeTitle()
  registerAPI()
  setNetworkAdapter()
  await startup()
  setNetworkAdapter()
}

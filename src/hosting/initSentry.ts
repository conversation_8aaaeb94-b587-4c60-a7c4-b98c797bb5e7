// 创建 Sentry 下线提示函数
function createSentryDeprecationError(methodName: string) {
  const errorMessage = `Sentry SDK 已下线，请移除对 window.Sentry.${methodName} 的调用。如需错误监控，请使用观测云监控服务。`
  console.error(errorMessage)
  throw new Error(errorMessage)
}

// 创建 Sentry 代理对象（兼容老旧浏览器）
function createSentryProxy() {
  // 检查是否支持 Proxy
  if (typeof Proxy !== 'undefined') {
    // 使用 Proxy 实现（现代浏览器）
    return new Proxy({}, {
      get(_target, prop) {
        // 对于任何属性访问，返回一个会抛出错误的函数
        return function(..._args: any[]) {
          createSentryDeprecationError(String(prop))
        }
      },

      set(_target, prop, _value) {
        // 阻止设置属性，并提示错误
        createSentryDeprecationError(`${String(prop)} (setter)`)
        return false
      },

      has(_target, _prop) {
        // 让外部代码认为属性存在，避免提前的 undefined 检查
        return true
      },

      ownKeys(_target) {
        // 返回常见的 Sentry 方法名，让外部代码认为这些方法存在
        return ['captureException', 'captureMessage', 'addBreadcrumb', 'setUser', 'setTag', 'setContext', 'configureScope', 'withScope', 'init']
      },

      getOwnPropertyDescriptor(_target, prop) {
        // 让外部代码认为属性是可配置和可枚举的
        return {
          enumerable: true,
          configurable: true,
          value: function(..._args: any[]) {
            createSentryDeprecationError(String(prop))
          }
        }
      }
    })
  } else {
    // 降级方案：使用 Object.defineProperty 创建通用拦截器
    const sentryObject: any = {}

    // 创建一个通用的错误函数
    const createErrorFunction = (methodName: string) => {
      return function(..._args: any[]) {
        createSentryDeprecationError(methodName)
      }
    }

    // 常见的 Sentry 方法列表
    const sentryMethods = [
      'captureException', 'captureMessage', 'addBreadcrumb', 'setUser', 'setTag',
      'setContext', 'configureScope', 'withScope', 'init', 'setLevel', 'setFingerprint',
      'setExtra', 'clearScope', 'pushScope', 'popScope', 'getCurrentHub', 'getClient',
      'captureEvent', 'lastEventId', 'flush', 'close', 'startTransaction', 'finishTransaction'
    ]

    // 为每个已知方法定义属性
    sentryMethods.forEach(methodName => {
      try {
        Object.defineProperty(sentryObject, methodName, {
          get: function() {
            return createErrorFunction(methodName)
          },
          enumerable: true,
          configurable: true
        })
      } catch (e) {
        // 如果 defineProperty 失败，直接赋值
        sentryObject[methodName] = createErrorFunction(methodName)
      }
    })

    // 尝试创建一个通用的属性拦截器（用于未知方法）
    if (typeof Object.defineProperty === 'function') {
      try {
        // 重写 hasOwnProperty 让所有属性看起来都存在
        Object.defineProperty(sentryObject, 'hasOwnProperty', {
          value: function(_prop: string) {
            return true
          },
          writable: false,
          enumerable: false,
          configurable: false
        })
      } catch (e) {
        // 忽略错误
      }
    }

    return sentryObject
  }
}

// 初始化Sentry
window.Sentry = createSentryProxy()
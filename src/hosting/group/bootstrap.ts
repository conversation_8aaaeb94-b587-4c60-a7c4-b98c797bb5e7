import { app } from '@ekuaibao/whispered'
import { addNcpcCode } from '@ekuaibao/lib/lib/addNcpcCode'
import { handleError, initGroupUser } from '../../lib/lib-util'
import { callback } from '../index.v0'
import { newHomeConfig } from '../newHomeConfig'
import { eKuaibaoSDK } from '@ekuaibao/sdk-bridge/sdk/ekuaibao'
import registerAPI from '../registerAPI'
import initializeTitle from '../initializeTitle'
import initPlatformInfo from '../initPlatformInfo'
import systemLimitLogout from '../systemLimitLogout'
import registerHandler from '../registerHandler'
import { fetchHandleError } from '../FetchConfig'
import { initRoute } from '../initRoute'
import { initColors } from '../initColors'
import setNetworkAdapter from '../setNetworkAdapter'
import initLogger from '../../logger/initLogger'
initLogger()
require('../../styles/app.less')
/********注册平台类型*******/
window.__PLANTFORM__ = 'APP'

window.inGroupApp = true // 判断是否在集团版中

function startup() {
  app.use({
    id: '@app',
    onready() {
      addNcpcCode()
    }
  })
  return callback()
}

fetchHandleError(handleError)

export async function bootstrap() {
  initGroupUser()
  initColors()
  initRoute()
  initPlatformInfo()
  systemLimitLogout()
  app.container.set('IDeviceType', 'DESKTOP')
  app.container.set('ISMessageEntry', false)
  app.sdk = app.container.get(eKuaibaoSDK)

  registerHandler()

  await startup()
  registerAPI()

  try {
    await (app.sdk as eKuaibaoSDK).initialize()
    setNetworkAdapter()
  } catch (e) {
    await newHomeConfig()
    setNetworkAdapter()
    return
  }
  await newHomeConfig()

  initializeTitle()
}

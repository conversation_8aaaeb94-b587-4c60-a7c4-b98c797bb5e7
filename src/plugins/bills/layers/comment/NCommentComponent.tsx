/**
 * @description 评论组件升级 原Mention在antd中已弃用，并且在云之家中会白屏
 * <AUTHOR>
 */
import { app as api } from '@ekuaibao/whispered'
import React, { PureComponent, useCallback, useState, useEffect } from 'react'
import { MentionsInput, Mention } from 'react-mentions'
import { forEach, cloneDeep } from 'lodash'
import { Form } from 'antd'
import { showMessage } from '@ekuaibao/show-util'
import { Staffs } from './../../type'
import classNames from './CommentComponent.module.less'
import { getDisplayName, getStaffName } from '../../../../elements/utilFn'
import { EnhanceConnect } from '@ekuaibao/store'
import { getBoolVariation, enableCommentOptimization } from '../../../../lib/featbit'

type IProps = {
  suggestionData: Staffs[]
  onChange: any
  form: any
  bus: any
  type: string
  required: boolean
  staffs?: Staffs[]
}
type IState = {
  staffMap: { [key: string]: Staffs }
  staffList: Staffs[]
  filterSuggestions: any[]
  list: any[]
  suggestionList: any[]
  allList: any[]
  filterScope: string,
  batchLoading:boolean,
}
const typePlaceholder = {
  comment: '请输入审批意见，可以通过空格+@提醒他人',
  mention: '添加评论（不超过 1400 个字，可通过空格+@姓名提醒其他人查看）'
}

const typeText = {
  comment: '审批意见',
  mention: '评论内容'
}

const TEXT_MAX_LENGTH = 1400

const enumType = {
  filterScope: 'UNLIMITED',
}



@EnhanceConnect(state => ({
  staffs: state['@common'].staffs
}))

// @ts-ignore
// @EnhanceFormCreate()
export default class CommentComponent extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props)
    this.state = {
      staffMap: null,
      staffList: [],
      filterSuggestions: [],
      list: [],
      suggestionList: [],
      allList: [],
      filterScope: '',
      batchLoading: getBoolVariation('batch-loading-mention')
    }
  }

  async componentDidMount() {
    const { bus, suggestionData } = this.props;
    bus.watch('get:mention:content', this.handleMentionResult);

    const suggestionIds = suggestionData?.map((item: Staffs) => this.fnFormatStaffId(item?.id)).filter(Boolean);
    await this.loadCommonStaffsVisibility(suggestionIds);

    const res = await api.dataLoader('@common.organizationConfig').load();
    if(!res){
      return
    }
    const filterScope = res.filterScope;
    this.setState({ filterScope });
    if (filterScope === enumType.filterScope) {
      await this.loadStaffsVisibility();
    }
  }


  componentWillUnmount() {
    const { bus } = this.props
    bus.un('get:mention:content', this.handleMentionResult)
  }

  fnFormatStaffId = (id: string = '', decode = false): string => {
    if (id?.includes('$$')) {
      // 人员的ID里面有两个$$，在Mention这个组件里面会被正则替换为一个$导致找不到人员信息
      return encodeURIComponent(id)
    }
    if (decode) {
      return decodeURIComponent(id)
    }
    return id
  }

  handleMentionResult = () => {
    return new Promise((resolve, reject) => {
      const { required, type } = this.props
      const { validateFields } = this.props.form
      validateFields((errors, values) => {
        if (!!errors) {
          return reject()
        }
        if (values && values.comment?.value) {
          const {
            comment: { value, mentions }
          } = values
          if ((!value || (value && value.length === 0)) && required) {
            showMessage.error(i18n.get(`${typeText[type]}不能为空`))
            return reject()
          }
          const participants = mentions.map(mention => this.fnFormatStaffId(mention.id, true))
          resolve({ comment: decodeURIComponent(value.replace(/%/g, '%25')), participants })
        }
        return resolve({})
      })
    })
  }

  private formatStaffList = staffs => {
    return Object.keys(staffs).map(key => {
      const staffName = getStaffName(staffs[key])
      const deptName = getDisplayName(staffs[key]?.defaultDepartment)
      return {
        display: staffName,
        id: this.fnFormatStaffId(staffs[key].id),
        avatar: staffs[key].avatar,
        deparmentName: deptName,
        deparmentId: staffs[key].defaultDepartment?.id
      }
    })
  }

  checkMention = (rule, value, callback) => {
    const contentString = value?.value ?? ''
    const { required, type } = this.props
    if (!contentString && required) {
      callback(i18n.get(`${typeText[type]}不能为空`))
    }
    const len = this.fnGetCheckLength(value)
    if (!enableCommentOptimization() && len > TEXT_MAX_LENGTH) {
      callback(new Error(i18n.get(`${typeText[type]}不能超过 ${TEXT_MAX_LENGTH} 个字`)))
    } else {
      callback()
    }
  }

  fnGetCheckLength = mentionValue => {
    const mentions = mentionValue?.mentions ?? []
    let str = mentionValue?.value ?? ''
    mentions.forEach(item => {
      const reg = new RegExp(`{${item.id}}`, 'g')
      str = str.replace(reg, `${item.display}`)
    })
    return str.length
  }
  private fnGetClickName = (...args) => {
    const { staffMap } = this.state
    const staff = staffMap[args[0]]
    const name = getStaffName(staff)
    return `@${name?.trim() ?? ''}`
  }

  private async loadCommonStaffsVisibility(suggestionIds: string[]) {
    // staffsVisibility 在单据主体中数据已经加载，和后端确认staffsVisibility 与 commonStaffsVisibility逻辑一致，只有allowHoseVisibility传参的区别，目前未使用
    const staffService = this.state.batchLoading ? 'staffsVisibility' : 'commonStaffsVisibility'
    const staffList = await api.dataLoader(`@common.${staffService}`).load();
    const staffMap = this.createStaffMap(staffList);
    const allList  = this.formatStaffList(staffMap);
    const suggestionList = allList.filter(item => (suggestionIds || []).includes(item.id));
    let list = allList
    if(this.state.batchLoading){
      list = list.slice(0,100)
    }
    this.setState({ staffMap, staffList, list: list, allList: allList, suggestionList });
  }

  private async loadStaffsVisibility() {
    let staffList = this.props.staffs
    if(!staffList){
      staffList = await api.dataLoader('@common.staffsVisibility').load();
    }
    const staffsVisibilityMap = this.createStaffMap(staffList);
    const allList = this.formatStaffList(staffsVisibilityMap);
    this.setState({ staffMap: staffsVisibilityMap, staffList: [...this.state.staffList, ...staffList], allList });
  }

  private createStaffMap(staffList: Staffs[]) {
    const staffMap = {};
    forEach(staffList, (staff: any) => {
      const newStaff = { ...staff, displayName: this.fnFormatStaffId(staff?.id) };
      staffMap[newStaff.displayName] = newStaff;
    });
    return staffMap;
  }

  render() {
    const { list, allList, filterScope } = this.state
    const { type = 'mention' } = this.props
    const { getFieldDecorator } = this.props.form
    return (
      <Form layout="horizontal">
        <Form.Item>
          {getFieldDecorator('comment', {
            rules: [{ validator: this.checkMention }]
          })(
            <MentionComponentUse
              data={list}
              displayTransform={this.fnGetClickName}
              type={type}
              allList={allList}
              filterScope={filterScope}
            />
          )}
        </Form.Item>
      </Form>
    )
  }
}

interface MentionComponentUseProps<T> {
  data: Array<T & { display: string }>
  value?: any
  type: string
  onChange?: (value: any) => void
  displayTransform: (args) => void
  allList: Array<T & { display: string }>
  filterScope: string
}

const MentionComponentUse = (props: MentionComponentUseProps<Staffs>) => {
  const { data, displayTransform, value = '', onChange, type, allList, filterScope } = props;
  const [targetValue, onTargetChange] = useMentionValue(value);
  const [filteredData, setFilteredData] = useState(data);
  const [currentLength, setCurrentLength] = useState(0);

  // 计算实际字符长度（包括@提及的显示名称）
  const calculateLength = (value: string, mentions: any[]) => {
    let str = value || '';
    mentions.forEach(item => {
      const reg = new RegExp(`{${item.id}}`, 'g');
      str = str.replace(reg, `${item.display}`);
    });
    return str.length;
  };

  const handleChange = (event: any, newValue: any, newPlainTextValue: string, mentions: any[]) => {
    const calculatedLength = calculateLength(newValue, mentions);
    
    // 先更新状态，让内容能够输入进去
    setCurrentLength(calculatedLength);
    onTargetChange(event, newValue, newPlainTextValue, mentions);
    onChange && onChange({ value: newValue, mentions });
    
    // 如果超过最大长度，进行截断处理
    if (enableCommentOptimization() && calculatedLength > TEXT_MAX_LENGTH) {
      // 从后往前逐个移除字符，直到长度符合要求
      let truncatedValue = newValue
      let truncatedMentions = Array.from(mentions)

      while (calculateLength(truncatedValue, truncatedMentions) > TEXT_MAX_LENGTH) {
        // 如果最后一个字符是@提及的一部分，移除整个提及
        const lastChat = truncatedValue[truncatedValue.length - 1]
        if (lastChat === '}') {
          const lastMentionIndex = truncatedValue.lastIndexOf('{')
          truncatedValue = truncatedValue.substring(0, lastMentionIndex)
          truncatedMentions.pop()
        } else {
          truncatedValue = truncatedValue.substring(0, truncatedValue.length - 1)
        }

        // 防止无限循环
        if (truncatedValue.length === 0) {
          break
        }
      }

      const finalLength = calculateLength(truncatedValue, truncatedMentions)
      setCurrentLength(finalLength)
      onTargetChange(event, truncatedValue, truncatedValue, truncatedMentions)
      onChange && onChange({ value: truncatedValue, mentions: truncatedMentions })
      return
    }
    
    if(getBoolVariation('batch-loading-mention')){
      if(newPlainTextValue.trim().endsWith('@')){
        setFilteredData(data) // 100 条数据
      }else{
        setFilteredData(allList) // 所有数据
      }
    }else{
      setFilteredData(data)
    }

    if(filterScope===enumType.filterScope){
      handleSearch(newPlainTextValue)
    }
  }

  const processString = (str: string) => {
    str = str.trim();

    if (str === '@' || str.endsWith(' @')) {
      return { isData: true, searchText: '' };
    }

    const atIndex = str.lastIndexOf('@');
    if (atIndex !== -1 && atIndex < str.length - 1) {
      const afterAt = str.slice(atIndex + 1).trim();
      if (afterAt) {
        return { isData: false, searchText: afterAt };
      }
    }

    return { isData: false, searchText: '' };
  };

  const handleSearch = (newPlainTextValue: string) => {
    const lines = newPlainTextValue.split('\n');
    lines.forEach(line => {
      const { isData, searchText } = processString(line);
      if (isData || filterScope !== enumType.filterScope) {
        setFilteredData(data);
      } else {
        setFilteredData(allList.filter(item => item.display === searchText));
      }
    });
  };

  return (
    <>
      <MentionComponent
        value={targetValue}
        onChange={handleChange}
        data={filteredData}
        displayTransform={displayTransform}
        type={type}
      />
      {enableCommentOptimization() && (
        <div className={classNames.characterCount}>
          {currentLength}/{TEXT_MAX_LENGTH}
        </div>
      )}
    </>
  );
};


import { Avatar } from '@hose/eui';

const MentionComponent = ({ value = '', data, onChange, displayTransform, type }) => {
  return (
    <MentionsInput
      value={value}
      className={'mentions'}
      classNames={classNames}
      onChange={onChange}
      placeholder={i18n.get(typePlaceholder[type])}
    >
      <Mention
        trigger="@"
        className={classNames.mentions__mention}
        displayTransform={displayTransform}
        markup="{__id__}"
        data={data}
        renderSuggestion={(suggestion, search, highlightedDisplay, index, focused) => (
          <>
            <Avatar
              size="small"
              shape="circle"
              style={{ height: 16, width: 16, marginRight: 5, lineHeight: '16px' }}
              src={suggestion?.avatar}
            />
            <span>{highlightedDisplay}</span>
            {suggestion.deparmentName && <span>({suggestion.deparmentName})</span>}
          </>
        )}
        appendSpaceOnAdd={true}
      />
    </MentionsInput>
  )
}

function useMentionValue(initialValue) {
  const [value, setValue] = useState(initialValue)
  const onChange = useCallback((_, newValue) => setValue(newValue), [setValue])
  return [initialValue.value, onChange]
}

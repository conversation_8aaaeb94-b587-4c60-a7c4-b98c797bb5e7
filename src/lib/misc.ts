import { app as api, app } from '@ekuaibao/whispered'
import { moneyStrToStandardCurrencyMoney } from '@ekuaibao/money-math'
import { get, isObject } from 'lodash'
import Big from 'big.js'
import { MoneyIF } from '@ekuaibao/ekuaibao_types'

const isWx = window.__PLANTFORM__ === 'WEIXIN'

export function getMoney(money: any) {
  return money ? (typeof money === 'object' ? (money.standard ? money.standard : '0.00') : money) : '0.00'
}

export function isMoney(obj: any) {
  return obj && typeof obj === 'object' && (obj.standard || obj.standard === 0) //有的时候返回的金额里面standard为number类型的0
}

export function fnDefineIsFormula(field: any, autoCalFields: any = {}, fieldMeta: any) {
  const { onFields = [], oldOnFields = [] } = autoCalFields
  const isFormulaAttr =
    Array.isArray(fieldMeta?.configs) && !!~fieldMeta?.configs.findIndex(el => el.ability === 'caculate')
  const isFormula = fieldMeta?.defaultValue && fieldMeta?.defaultValue.type === 'formula' //系统计算
  const isOldCostStandard = oldOnFields?.includes(field) && !isFormula //旧差标
  // 税额、金额字段  有隐藏必填公式  1、没有配置老差旅标准即oldOnFields不下发amount  2、没有系统计算公式即type!=formula
  // 硬性返回false
  const type = get(fieldMeta, 'dataType.elemType.type') || get(fieldMeta, 'dataType.type')
  if (type === 'money') {
    if (isFormulaAttr && fieldMeta?.editable && !isFormula && !isOldCostStandard) {
      return false
    } else {
      return (
        onFields &&
        !!~onFields.indexOf(field) &&
        !(fieldMeta && fieldMeta.editable && fieldMeta.defaultValue && fieldMeta.defaultValue.type === 'formula')
      )
    }
  }
  return (
    onFields &&
    !!~onFields.indexOf(field) &&
    !(fieldMeta && fieldMeta.editable && fieldMeta.defaultValue && fieldMeta.defaultValue.type === 'formula')
  )
}

export function standardValueMoney(value: any, currency?: any) {
  const standardCurrency = currency ? currency : api.getState()['@common'].standardCurrency
  const { strCode, numCode, symbol, unit, scale } = standardCurrency
  return {
    standard: value,
    standardStrCode: strCode,
    standardNumCode: numCode,
    standardSymbol: symbol,
    standardUnit: unit,
    standardScale: scale
  }
}

export function isStandardCurrencyDiff(source, target) {
  return source?.standardNumCode !== target?.standardNumCode
}

export function foreignConversionStandard(target: MoneyIF) {
  const { currency = {}, rates = [] } = api.getState()['@bills'].dimentionCurrencyInfo
  const rate = rates?.find(rate => rate.strCode === target?.standardStrCode)
  if (currency?.numCode === target?.foreignNumCode && rate) {
    return {
      ...target,
      rate: rate.rate,
      standardStrCode: target?.foreignStrCode,
      standardNumCode: target?.foreignNumCode,
      standardSymbol: target?.foreignSymbol,
      standardUnit: target?.foreignUnit,
      standard: target?.foreign,
      foreign: target?.standard,
      foreignNumCode: target?.standardNumCode,
      foreignStrCode: target?.standardStrCode,
      foreignSymbol: target?.standardSymbol,
      foreignUnit: target?.standardUnit
    }
  } else if (currency?.numCode !== target?.standardNumCode && target?.foreignNumCode) {
    const rate = rates.find(rate => rate.numCode === target?.foreignNumCode) || {}
    const foreign = target?.foreign
    const standard = Number(Number(foreign) * Number(rate.rate)).toFixed(rate.scale)
    return {
      ...target,
      rate: rate.rate,
      standardStrCode: currency?.strCode,
      standardNumCode: currency?.numCode,
      standardSymbol: currency?.symbol,
      standardUnit: currency?.unit,
      standard,
      foreign,
      foreignNumCode: rate.numCode,
      foreignStrCode: rate.strCode,
      foreignSymbol: rate.symbol,
      foreignUnit: rate.unit
    }
  }
  return target
}

// 初始化外币和本位币数据
export function initializeCurrencyData(foreignCurrency, standardCurrency, value) {
  value = value || {}
  return {
    rate: standardCurrency?.rate ?? null,
    standardStrCode: standardCurrency?.strCode,
    standardNumCode: standardCurrency?.numCode,
    standardSymbol: standardCurrency?.symbol,
    standardUnit: standardCurrency?.unit,
    standardScale: standardCurrency?.scale,
    standard: value?.standard,
    sysRate: standardCurrency?.rate ?? null,
    foreign: value?.foreign,
    foreignNumCode: foreignCurrency?.numCode,
    foreignStrCode: foreignCurrency?.strCode,
    foreignSymbol: foreignCurrency?.symbol,
    foreignUnit: foreignCurrency?.unit,
    foreignScale: foreignCurrency?.scale,
    ...value,
  }
}

// 两个币种的本位币不一样时
// 1、如果系统中配置的外币中有人民币：则将发票金额赋值给明细中金额的原币，本位币根据系统的汇率计算得出
// 2、如果系统中配置的外币中没有人民币：则将发票的金额直接复制给明细中金额的本位币
export function formatMoneyWithDiffCurrency(money, targetMoney, rates) {
  const allCurrencyRates = api.getState()['@common'].allCurrencyRates
  const currencyRate = (rates || allCurrencyRates)?.find(el => el.numCode === money.standardNumCode)
  if (!!currencyRate) {
    const standard = Big(money.standard)
      .times(currencyRate.rate)
      .toFixed(targetMoney.standardScale)
    return {
      ...targetMoney,
      standard,
      foreign: money.standard,
      foreignNumCode: money.standardNumCode,
      foreignScale: money.standardScale,
      foreignStrCode: money.standardStrCode,
      foreignSymbol: money.standardSymbol,
      foreignUnit: money.standardUnit,
      rate: currencyRate.rate,
      sysRate: currencyRate.rate
    }
  }
  return {
    ...targetMoney,
    standard: isObject(money) ? money?.standard : money,
  }
}

export function getAcountKey(value: any) {
  const kv: any = {
    taxRate: 'taxAmount',
    noTaxAmount: 'noTaxAmount',
    taxTotal: 'taxTotal'
  }

  return kv[value]
}

export function moneyStr2StandardMoneyValue(money = '0.00') {
  if (!!~['number', 'string'].indexOf(typeof money)) {
    const standCurrency = api.getState('@common.standardCurrency')
    return moneyStrToStandardCurrencyMoney(money, standCurrency)
  }
  return money
}

export function isIE() {
  //@ts-ignore
  return !!(window.ActiveXObject || 'ActiveXObject' in window || navigator.userAgent.indexOf('MSIE') > -1)
}

export const getDataSource = (key: string) => {
  const dataSource = isWx ? session.get(key) : localStorage.getItem(key)
  return dataSource ? JSON.parse(dataSource) : {}
}

export function calculateColCountByOffsetWidth({
  offsetWidth,
  layoutBtnGroupMap,
  size = { isFirstLocal: false, isFirstResize: false },
  layoutSizeMap
}) {
  const webShow = getDataSource('170043_webShow')
  if (webShow) {
    if (webShow.forbidStaffModify) {
      return webShow.formNumber
    } else {
      return layoutBtnGroupMap['form-col-count'] || webShow.formNumber || 2
    }
  }

  let colCount: number
  const minSmallFilter = offsetWidth < layoutSizeMap.small.min
  const maxSmallFilter = offsetWidth >= layoutSizeMap.small.min && offsetWidth < layoutSizeMap.small.max
  const middleFilter =
    (offsetWidth >= layoutSizeMap.middle.min && offsetWidth <= layoutSizeMap.middle.max) ||
    offsetWidth >= layoutSizeMap.middle.max
  if (minSmallFilter) {
    colCount = 1
  } else if (maxSmallFilter) {
    colCount = layoutSizeMap.small.colCount
  } else if (middleFilter) {
    colCount = layoutSizeMap.middle.colCount
  } else {
    colCount = layoutSizeMap.small.colCount
  }
  const localColCount = Number(layoutBtnGroupMap['form-col-count'] || colCount)
  if (size.isFirstLocal) {
    //local 优先
    return offsetWidth >= layoutSizeMap.middle.min || localColCount < layoutSizeMap.small.colCount
      ? localColCount
      : colCount
  } else if (size.isFirstResize) {
    //响应式优先
    return colCount
  } else {
    // colCount =
    //   colCount === localColCount ||
    //   offsetWidth >= layoutSizeMap.middle.max ||
    //   offsetWidth >= layoutSizeMap.middle.min ||
    //   offsetWidth < layoutSizeMap.small.max
    //     ? colCount
    //     : localColCount
    // isFirstResize 只屏幕宽度变化才走响应式,屏幕高度变化还走 local 配置
    return localColCount
  }
}

// 配置金额币种自定义规则使用
export function standardMoneyByForeignWithRlue(
  foreign: string,
  foreignNumCode: number,
  changeRate: number,
  amount: string
) {
  const historyCurrencyInfo = api.getState()['@bills'].historyCurrencyInfo
  const dimentionCurrency = api.getState()['@bills']?.dimentionCurrencyInfo
  const allCurrencyRates = api.getState()['@common']?.allCurrencyRates
  const standardCurrency = dimentionCurrency?.currency
    ? dimentionCurrency?.currency
    : api.getState()['@common']?.standardCurrency
  const allStandardCurrency = allCurrencyRates.filter(el => el.originalId === standardCurrency.numCode)
  let rates = dimentionCurrency?.rates ? dimentionCurrency?.rates : allStandardCurrency
  if (historyCurrencyInfo && historyCurrencyInfo.currency === standardCurrency.numCode) {
    rates = historyCurrencyInfo.rates
  }
  const rate = rates?.find(rate => rate.numCode === foreignNumCode)
  if (rate) {
    const __rate = rate.rate
    const standard = amount || new Big(foreign).times(__rate).toFixed(standardCurrency?.scale)
    return {
      standard,
      standardStrCode: standardCurrency?.strCode,
      standardNumCode: standardCurrency?.numCode,
      standardSymbol: standardCurrency?.symbol,
      standardUnit: standardCurrency?.unit,
      standardScale: standardCurrency?.scale,
      rate: __rate,
      sysRate: rate?.rate,
      foreign: Number(foreign).toFixed(rate.scale),
      foreignNumCode: rate?.numCode,
      foreignScale: rate?.scale,
      foreignStrCode: rate?.strCode,
      foreignSymbol: rate?.symbol,
      foreignUnit: rate?.unit
    }
  }
  return standardValueMoney(amount || foreign, standardCurrency)
}

export function standardMoneyByForeign(foreign: string, foreignNumCode: number, changeRate?: number) {
  const dimentionCurrency = api.getState()['@bills']?.dimentionCurrencyInfo
  const allCurrencyRates = api.getState()['@common']?.allCurrencyRates
  const standardCurrency = dimentionCurrency?.currency
    ? dimentionCurrency?.currency
    : api.getState()['@common']?.standardCurrency
  const rates = dimentionCurrency?.rates ? dimentionCurrency?.rates : allCurrencyRates
  const rate = rates?.find(rate => rate.numCode === foreignNumCode)
  if (rate) {
    const __rate = changeRate || rate.rate
    const standard = new Big(foreign).times(__rate).toFixed(standardCurrency?.scale)
    return {
      standard,
      standardStrCode: standardCurrency?.strCode,
      standardNumCode: standardCurrency?.numCode,
      standardSymbol: standardCurrency?.symbol,
      standardUnit: standardCurrency?.unit,
      standardScale: standardCurrency?.scale,
      rate: __rate,
      sysRate: rate?.rate,
      foreign: Number(foreign).toFixed(rate.scale),
      foreignNumCode: rate?.numCode,
      foreignScale: rate?.scale,
      foreignStrCode: rate?.strCode,
      foreignSymbol: rate?.symbol,
      foreignUnit: rate?.unit
    }
  }
  return standardValueMoney(foreign)
}

export function isZero(value) {
  if (!value) {
    return false
  }
  if (typeof value === 'string') {
    return value === '0' || value === '0.00'
  }
  if (typeof value === 'number') {
    return value === 0
  }
  return false
}

export function standardCurrencyToForeign(standardMoneyStr, money) {
  // 外币=本位币/汇率
  // tslint:disable-next-line:prefer-const
  let { foreignScale, rate, standardScale, foreignNumCode, budgetNumCode, budgetRate, budgetScale } = money
  const standardMoneyBig = new Big(standardMoneyStr)
  const moneyObj = { ...money, standard: standardMoneyBig.toFixed(Number(standardScale)) }
  if (foreignNumCode) {
    moneyObj.foreign = standardMoneyBig.div(rate).toFixed(Number(foreignScale))
  }
  if (budgetNumCode && budgetRate) {
    moneyObj.budget = standardMoneyBig.div(budgetRate).toFixed(Number(budgetScale))
  }
  return moneyObj
}


export function convertConsumeStandardMoney(resultValue, currencyInfo) {
  const dimentionCurrency = api.getState()['@bills']?.dimentionCurrencyInfo
  const allCurrencyRates = api.getState()['@common']?.allCurrencyRates
  const standardCurrency = dimentionCurrency?.currency
    ? dimentionCurrency?.currency
    : api.getState()['@common']?.standardCurrency

  if (standardCurrency?.numCode === currencyInfo?.multiNumCode) {
    const standardScale = standardCurrency?.scale !== undefined ? standardCurrency?.scale : 2
    return {
      standard: new Big(resultValue).toFixed(standardScale),
      standardStrCode: standardCurrency?.strCode,
      standardNumCode: standardCurrency?.numCode,
      standardSymbol: standardCurrency?.symbol,
      standardUnit: standardCurrency?.unit,
      standardScale: standardCurrency?.scale
    }
  }
  const allStandardCurrency = allCurrencyRates.filter(el => el.originalId === standardCurrency.numCode)
  let rates = dimentionCurrency?.rates ? dimentionCurrency?.rates : allStandardCurrency
  const foreignCurrency = rates?.find(rate => rate.numCode === currencyInfo?.multiNumCode)
  if (foreignCurrency) {
    const foreignScale = foreignCurrency?.scale !== undefined ? foreignCurrency?.scale : 2
    const standardScale = standardCurrency?.scale !== undefined ? standardCurrency?.scale : 2
    return {
      foreign: new Big(resultValue).toFixed(foreignScale),
      foreignNumCode: foreignCurrency?.numCode,
      foreignStrCode: foreignCurrency?.strCode,
      foreignSymbol: foreignCurrency?.symbol,
      foreignUnit: foreignCurrency?.unit,
      foreignScale: foreignCurrency?.scale,
      rate: foreignCurrency?.rate,
      sysRate: foreignCurrency?.rate,
      standard: new Big(resultValue).times(foreignCurrency?.rate).toFixed(standardScale),
      standardStrCode: standardCurrency?.strCode,
      standardNumCode: standardCurrency?.numCode,
      standardSymbol: standardCurrency?.symbol,
      standardUnit: standardCurrency?.unit,
      standardScale: standardCurrency?.scale
    }
  }

  return {
    standardStrCode: standardCurrency?.strCode,
    costStandardError: true
  }
}
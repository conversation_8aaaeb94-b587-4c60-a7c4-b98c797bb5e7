import { app } from '@ekuaibao/whispered'
import { addNcpcCode } from '@ekuaibao/lib/lib/addNcpcCode'
import { callback } from '../index.v0'
import { newHomeConfig } from '../newHomeConfig'
import { MCEkuaibaoSDK } from '@ekuaibao/sdk-bridge/sdk/ekuaibao/indexMC'
import registerAPI from '../registerAPI'
import initializeTitle from '../initializeTitle'
import initPlatformInfo from '../initPlatformInfo'
import systemLimitLogout from '../systemLimitLogout'
import registerHandler from '../registerHandler'
import { initRoute } from '../initRoute'
import setNetworkAdapter from '../setNetworkAdapter'
import { fetchHandleError } from '../FetchConfig'
import { initColors } from '../initColors'
import { initUserInfo } from '../../lib/lib-util'
import initLogger from '../../logger/initLogger'
initLogger()
/********注册平台类型*******/
window.__PLANTFORM__ = 'MC'

function startup() {
  app.use({
    id: '@mc',
    onready() {
      addNcpcCode()
    }
  })
  callback()
}

fetchHandleError()
export async function bootstrap() {
  initUserInfo(false)
  initColors()
  initRoute()
  initPlatformInfo()
  systemLimitLogout()
  app.container.set('IDeviceType', 'DESKTOP')
  app.container.set('ISMessageEntry', false)
  app.sdk = app.container.get(MCEkuaibaoSDK)
  registerHandler()
  startup()
  registerAPI()
  try {
    await (app.sdk as MCEkuaibaoSDK).initialize()
    setNetworkAdapter()
  } catch (e) {
    console.log(e)
    await newHomeConfig()
    setNetworkAdapter()
    return
  }
  await newHomeConfig()

  initializeTitle()
}

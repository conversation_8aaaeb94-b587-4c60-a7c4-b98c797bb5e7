import React from 'react'
import { Tag } from '@hose/eui'
import { OutlinedDirectionRight } from '@hose/eui-icons'
import styles from './ai-audit-summary.module.less'
import { RiskWarningItem } from '../type'
import { getAIAuditSummary } from './utils'

interface AIAuditSummaryProps {
  items: RiskWarningItem[]
  onClick: () => void
}

export const AIAuditSummary = ({ items, onClick }: AIAuditSummaryProps) => {
  const { total, successCount, riskCount, errorCount } = getAIAuditSummary(items)

  if (total === 0) {
    return null
  }

  return <div className={styles['ai-audit-summary']} onClick={onClick}>
    <Tag size='small' className={styles['ai-audit-summary-tag']}>
      <span className={styles['gradient-text']}>{i18n.get('AI 增强')}</span>
    </Tag>
    <span className={styles['ai-audit-summary-title-text']}>
      {i18n.get('AI 共计审核{total}条规则。其中{successCount}条审核通过，{riskCount}条风险，{errorCount}条错误。', {
        total,
        successCount,
        riskCount,
        errorCount
      })}
    </span>
    <span className={styles['ai-audit-summary-detail-text']}>{i18n.get('详情')}</span>
    <OutlinedDirectionRight />
  </div>

}
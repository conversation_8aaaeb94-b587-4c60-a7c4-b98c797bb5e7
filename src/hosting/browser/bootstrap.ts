/*!
 * Copyright 2019 yangjunbao <<EMAIL>>. All rights reserved.
 * @since 2019-07-09 12:08:12
 */

import { app } from '@ekuaibao/whispered'
import { addNcpcCode } from '@ekuaibao/lib/lib/addNcpcCode'
import { goto } from '../../lib/lib-util'
import { callback } from '../index.v0'
import { newHomeConfig } from '../newHomeConfig'
import BrowserSDK from '@ekuaibao/sdk-bridge/sdk/browser'
import registerAPI from '../registerAPI'
import { initializeTitle } from '../initializeTitle'
import initPlatformInfo from '../initPlatformInfo'
import systemLimitLogout from '../systemLimitLogout'
import registerHandler from '../registerHandler'
import { FetchError, fetchHandleError } from '../FetchConfig'
import { initRoute } from '../initRoute'
import { initColors } from '../initColors'
import setNetworkAdapter from '../setNetworkAdapter'
import initLogger from '../../logger/initLogger'
import { initDatafluxRum } from '../dataflux-rum'

/********注册平台类型*******/
window.__PLANTFORM__ = 'DEBUGGER'

initDatafluxRum();
initLogger()
function startup() {
  app.use({
    id: '@browser',
    onready() {
      addNcpcCode()
    }
  })
  callback()
}

// hook ajax response handler
function unAuthorizedAction(error: FetchError) {
  if (error.status === 401) {
    // 未登录
    goto('/login')
  }
}

fetchHandleError(unAuthorizedAction)

export async function bootstrap() {
  initColors()
  app.container.set('IDeviceType', 'DESKTOP')
  app.container.set('ISMessageEntry', false)
  app.sdk = app.container.get(BrowserSDK)

  registerHandler()

  await (app.sdk as BrowserSDK).initialize()
  await newHomeConfig()
  initRoute()
  initPlatformInfo()
  systemLimitLogout()
  startup()
  initializeTitle()
  registerAPI()
  setNetworkAdapter()
}

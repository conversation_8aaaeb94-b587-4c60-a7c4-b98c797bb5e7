/*!
 * Copyright 2019 yangjunbao <yang<PERSON><PERSON>@shimo.im>. All rights reserved.
 * @since 2019-07-09 12:02:42
 */

import { app } from '@ekuaibao/whispered'
import { initDatafluxRum } from '../dataflux-rum'
import { addNcpcCode } from '@ekuaibao/lib/lib/addNcpcCode'
import { goto } from '../../lib/lib-util'
import { callback } from '../index.v0'
import { newHomeConfig } from '../newHomeConfig'
import { eKuaibaoSDK } from '@ekuaibao/sdk-bridge/sdk/ekuaibao'
import registerAPI from '../registerAPI'
import initializeTitle from '../initializeTitle'
import registerHandler from '../registerHandler'
import initPlatformInfo from '../initPlatformInfo'
import systemLimitLogout from '../systemLimitLogout'
import { initRoute } from '../initRoute'
import { initColors } from '../initColors'
import setNetworkAdapter from '../setNetworkAdapter'
import qs from 'qs'
import initLogger from '../../logger/initLogger'
import { anonymousUser, initFeatBit, switchFeatBitUser } from '../../lib/featbit'
import { initializeI18n, setStaffDefaultLanguage } from '../i18n-initialize'

require('../../styles/app.less')
/********注册平台类型*******/
window.__PLANTFORM__ = 'APP'

initDatafluxRum()
initLogger()

function startup() {
  const params = qs.parse(location.search.slice(1))
  if (params?.billentry) {
    window.__SUB_PLANNTFORM__ = 'BILLENTRY'
  }
  app.use({
    id: '@app',
    onready() {
      addNcpcCode()
    }
  })
  callback()
}

let initPromise = null
export const appDataInitialize = async () => {
  initPromise = appInitialize()
  await initPromise
}

async function appInitialize() {
  await Promise.all([initFeatBit(anonymousUser()), initPlatformInfo(), initializeI18n()])
  initColors()
  app.container.set('IDeviceType', 'DESKTOP')
  app.container.set('ISMessageEntry', false)
  app.sdk = app.container.get(eKuaibaoSDK)
  const idpBaseUri = window.getEnvConfigValue('idpDomain', app.sdk.idpInfo.idpBaseUri)
  app.sdk.idpInfo = {...app.sdk.idpInfo, idpBaseUri }
  registerHandler()
  registerAPI()
  try {
    await (app.sdk as eKuaibaoSDK).initialize()
    await switchFeatBitUser()
    initRoute()
    systemLimitLogout()
    setStaffDefaultLanguage()
    await newHomeConfig({ needInitI18n: false, needInitFeatBit: false })
    setNetworkAdapter()
    initializeTitle()
  } catch (e) {
    if (e?.errorCode === 401) {
      goto('/login')
    }
    console.error(e)
  }
}

export async function bootstrap() {
  if (initPromise) {
    await initPromise
    initPromise = null
  }
  // 初始化插件、以及DOM挂载
  startup()
}

import React, { useMemo, useState } from 'react'
import Overflow, { OverflowProps } from 'rc-overflow'
import { Button, ButtonProps, Dropdown, MenuProps } from '@hose/eui'
import styles from './FlexableButtonGroup.module.less'

interface FlexableButtonGroupProps extends OverflowProps<ButtonGroupItem> {
  buttons: ButtonGroupItem[]
  moreText?: string
  useFakeDisabled?: boolean
}

interface ButtonGroupItem {
  id?: string
  label: string
  main?: boolean
  dangerous?: boolean
  onClick: () => void
  disabled?: boolean
  minorGroup?: boolean
  // 兼容老的按钮组
  buttonProps?: ButtonProps
  action?: string
}

const isMain = (button: ButtonGroupItem) => {
  return button.main
}

const isNoGroup = (button: ButtonGroupItem) => {
  return button.minorGroup !== undefined && !button.minorGroup
}

const getActionType = (item: ButtonGroupItem) => {
  let actionType = item.id || item.label
  const action = item.action
  if (action && action.includes('freeflow')) {
    actionType = action.split('freeflow.')[1]
  }
  return actionType
}

/**
 * FlexableButtonGroup 是一个灵活的按钮组，它可以根据按钮的数量自动调整按钮的宽度，
 * 当按钮数量超过一定数量时，会自动显示一个下拉菜单，点击下拉菜单可以显示所有按钮。
 *
 * 但这个有一个限制，不能设置最大显示宽度，但可以设置 max-width 来限制容器
 * 宽度来达到限制现实元素个数的目的，默认宽度为600px，大概最多5、6个按钮的样子。
 */
export const FlexableButtonGroup = ({
  buttons,
  moreText = i18n.get('更多'),
  useFakeDisabled,
  ...overflowProps
}: FlexableButtonGroupProps) => {
  const [lastVisibleIndex, setLastVisibleIndex] = useState(0)
  const sortedButtons = useMemo(
    () =>
      buttons.sort((a, b) => {
        if (isMain(a) && !isMain(b)) {
          return -1
        }
        if (!isMain(a) && isMain(b)) {
          return 1
        }
        if (isNoGroup(a) && !isNoGroup(b)) {
          return -1
        }
        if (!isNoGroup(a) && isNoGroup(b)) {
          return 1
        }
        return 0
      }),
    [buttons]
  )

  const renderButtonItem: OverflowProps<ButtonGroupItem>['renderItem'] = item => {
    const { label, main, dangerous, onClick, disabled, buttonProps, ...rest } = item
    return (
      <div className={styles['flexable-button-group-item']}>
        <Button
          category={main ? 'primary' : 'secondary'}
          theme={dangerous ? 'danger' : 'default'}
          data-testid={`flexable-button-${getActionType(item)}`}
          disabled={disabled}
          onClick={() => {
            if (useFakeDisabled && disabled) {
              // 不操作
            } else {
              onClick()
            }
          }}
          {...buttonProps}
          {...rest}
        >
          {label}
        </Button>
      </div>
    )
  }

  const renderRest = () => {
    const items: MenuProps['items'] = sortedButtons.slice(lastVisibleIndex + 1).map(btn => ({
      key: btn.id || btn.label,
      label: btn.label,
      onClick: btn.onClick,
      'data-testid': `flexable-button-${getActionType(btn)}`
    })) as MenuProps['items']

    return (
      <Dropdown menu={{ items }}>
        <Button category="secondary" theme="default">
          {moreText}
        </Button>
      </Dropdown>
    )
  }

  return (
    <Overflow
      data={sortedButtons}
      maxCount="responsive"
      className={styles['flexable-button-group']}
      renderItem={renderButtonItem}
      renderRest={renderRest}
      onVisibleChange={newLastIndex => {
        setLastVisibleIndex(newLastIndex)
      }}
      {...overflowProps}
    />
  )
}

@import '~@eku<PERSON>bao/eui-styles/less/token-mobile.less';

.risk-warning-info {
  background-color: #fff;
  padding: 16px;
  height: 100%;
  overflow-y: auto;
  :global {
    .title {
      color: var(--eui-text-title);
      font-weight: 500;
    }
    .reject-box {
      background-color: var(--eui-bg-body-overlay);
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 16px;

      &.withExtras {
        .status {
          color: var(--eui-text-caption) !important;
        }
      }
      &.agree {
        .status {
          color: var(--eui-function-success-600);
        }
      }
      &.is-ai-approval {
        background: linear-gradient(251deg, #EBF4FF 5.73%, #F3F2FF 96.93%);
      }
      .avatar {
        display: flex;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        justify-content: center;
        align-items: center;
      }
      .title {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        img {
          width: 16px;
          border-radius: 100px;
        }
      }
      .name {
        max-width: 80px;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-left: 5px;
        white-space: nowrap;
        color: var(--eui-text-title);
        font-weight: normal;
      }
      .status {
        color: var(--eui-function-danger-500);
        font-size: 14px;
        margin-left: 4px;
      }
      .reason {
        padding: 8px;
        border-radius: 4px;
        background-color: #fff;
        color: var(--eui-text-title);
        overflow-y: auto;
        word-break: break-all;
      }
    }
    .success {
      margin-top: 11px;
      .title {
        padding: 5px 0;
      }
      .success-item {
        padding: 16px;
        border: 1px solid var(--eui-line-divider-default);
        border-radius: 8px;
        background-color: #fff;
        color: var(--eui-text-caption);
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        padding-bottom: 8px;
        label {
          display: block;
          width: 49%;
          line-height: 20px;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-left: 2px;
          white-space: nowrap;
          margin-bottom: 8px;
        }
        span {
          color: var(--eui-function-success-500);
          font-size: 16px;
        }
      }
    }
    .result-title {
      color: var(--eui-text-title);
      background-color: #f7f8fa;
      padding: 12px;
      font-weight: 500;
      border-top-right-radius: 8px;
      border-top-left-radius: 8px;
    }
    .result {
      background-color: #f7f8fa;
      padding: 0 12px 12px 12px;
      display: flex;
      justify-content: space-between;
      border-bottom-right-radius: 8px;
      border-bottom-left-radius: 8px;
      .box {
        width: calc(50% - 6px);
        background-color: #fff;
        text-align: center;
        padding: 6px 0;
        color: var(--eui-function-warning-500);
        font-weight: 500;
        border-radius: 4px;
        height: 54px;
        line-height: 18px;
        .label {
          font-size: 12px;
        }
        &.error {
          .label,
          .num {
            color: var(--eui-function-danger-500);
          }
        }
        &.zero {
          .label,
          .num {
            color: var(--eui-text-placeholder);
          }
        }
        .num {
          font-weight: 700;
          font-size: 18px;
          font-family: DIN Alternate;
          margin-bottom: 4px;
        }
      }
    }

    .eui-collapse-header:hover {
      background-color: var(--eui-bg-body-overlay);
    }
    .warning-collapse {
      margin-top: 16px;
      background-color: #fff;

      .eui-collapse-content-box {
        padding: 0;
      }
      .eui-collapse-header-text {
        color: var(--eui-font-body-b1);
        font-weight: 500;
      }
      > .eui-collapse-item {
        > .eui-collapse-header {
          font-size: 12px;
          padding: 2px 6px;
          border-radius: 2px !important;
          line-height: 24px;
          margin-bottom: 4px;
          .eui-collapse-arrow {
            color: var(--eui-icon-n2);
          }

          .eui-collapse-expand-icon {
            width: 20px;
          }
        }
      }
    }

    .warning-item {
      margin-bottom: 8px;
      &:last-child {
        margin-bottom: 0;
      }
      .left {
        display: flex;
        width: calc(100% - 44px);
        padding: 1px 0;
        .eui-tag {
          max-width: 90px;
          height: 20px;
        }
        .warning-item-tag {
          @ai-assistance-tag-width: 87px;
          max-width: calc(100% - 10px - @ai-assistance-tag-width);
        }
      }
      .eui-button-mini {
        max-width: 50px;
      }
      .ai {
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(to right, #c9f6fb, #d7caff);
        background-color: #4e77fe;
        height: 20px;
        padding: 1px 4px;
        border-radius: 4px;
        color: #4e77fe;
        margin-left: 10px;
        font-size: 12px;
      }
      .title {
        color: var(--eui-text-title);
        font: var(--eui-font-body-b1);
      }
      .des {
        color: var(--eui-text-caption);
        font: var(--eui-font-note-r2);
        max-height: 320px;
        overflow-y: auto;
      }
    }
    .warning-item-warning{
      background: linear-gradient(90deg, rgba(254, 245, 227, 0.50) 0%, rgba(255, 253, 250, 0.50) 100%);
    }
  }
}

// 新版风险tag 目前用于单据主体上
.risk-tag-new {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  left: 0;
  bottom: -4px;
  max-width: 100%;
  :global {
    .inner-wrapper {
      display: flex;
      align-items: center;
      margin-top: 4px;
      padding: 1px 0 1px 8px;
      line-height: 24px;
      border-radius: 4px;
      font-size: 12px;
      text-align: center;
      max-width: 100%;
      color: var(--eui-text-caption);
      background-color: var(--eui-function-warning-50);
      .icon {
        color: var(--eui-function-warning-500);
      }
    }
    .error {
      background-color: var(--eui-function-danger-50);
      .icon {
        color: var(--eui-function-danger-500);
      }
    }
    .number {
      min-width: 120px;
      margin-left: 4px;
      text-align: left;
      flex: 1;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .more {
      width: 27px;
      font-weight: 400;
      font-size: 12px;
      line-height: 24px;
    }
  }
}

.risk-tag-Popover {
  min-width: 240px;
  max-width: 400px;
}

.content-msg-tips {
  color: var(--eui-text-placeholder, rgba(29, 33, 41, 0.50));
  font: var(--eui-font-body-r1);
  border-bottom: 1px dashed var(--eui-line-divider-default);
  padding-bottom: 4px;
  margin-bottom: 4px;
}

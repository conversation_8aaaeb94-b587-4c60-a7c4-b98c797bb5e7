import React, { memo } from 'react'
import { Tooltip } from '@hose/eui'
import { riskTypeMap, riskColorMap, getRiskComponent } from './utils'
import styles from './RiskWarningItem.module.less'
import { Tag } from '@hose/eui'
import { isErrorItem } from './ai-audit-result/utils'
interface IProps {
  risk: any;
  riskType?: string;
  riskName?: string;
  riskStatus?: string;
  riskVersion?: number;
  children?: any;
}
interface TitleProps {
  riskType: string;
  riskName?: string;
  riskVersion?: number;
}

const RiskWarningItem: React.FC<IProps> = memo(props => {
  const { risk = {}, riskType = '', riskName = '', riskStatus = '', riskVersion = 0, children = null } = props
  const name = riskName || risk.controlName
  let version = riskVersion || risk.controlVersion
  const type = riskType || risk.type
  const status = riskStatus || (isErrorItem(risk) ? 'error' : 'warn')
  const classnames = riskColorMap[type][status] || ''
  const componentMap = getRiskComponent()
  if (type === 'costControl' && version) {
    const ruleDetail = risk?.ruleDetail ?? ''
    if (ruleDetail.indexOf('版本') > -1 || ruleDetail.indexOf('version') > -1) {
      version = 0
    }
  }
  return (
    <div className={`${styles['risk-warning-item']} ${styles[classnames]}`}>
      <RiskWarningItemTitle riskType={type} riskName={name} riskVersion={version} />
      <div className="risk-item-content">
        {componentMap[type]?.(risk) || children}
      </div>
    </div>
  )
})
export default RiskWarningItem

export const RiskWarningItemTitle = (props: TitleProps) => {
  const { riskType, riskName, riskVersion } = props
  return (
    <div className="risk-item-title">
      <Tag color="warning" className="mr-8" fill="invert" size="small">{riskTypeMap()[riskType]}</Tag>
      {riskName && (
        <Tooltip title={riskName}>
          <span className="risk-name">{i18n.get(riskName)}</span>
        </Tooltip>
      )}
      {riskVersion && riskVersion > 0 ? (
        <span className="risk-version"> {i18n.get(`[版本{__k0}]`, { __k0: riskVersion })}</span>
      ) : null}
    </div>
  )
}




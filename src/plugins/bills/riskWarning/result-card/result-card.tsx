import React from 'react'
import cx from 'classnames'
import { TwoToneGeneralCorrect, TwoToneGeneralError, TwoToneGeneralRisk } from '@hose/eui-icons'
import { Card, Theme } from '../card'
import { AuditResult } from './types'

import styles from './result-card.module.less'

interface ResultCardProps {
  result: {
    risk: number
    error: number
  }
  title?: React.ReactNode
  extra?: React.ReactNode
}

const ResultCard = ({ result, title = i18n.get('综合审查结果：'), extra }: ResultCardProps) => {
  const currentAuditResult = React.useMemo(() => {
    if (result.error > 0) {
      if (result.risk > 0) {
        return {
          state: AuditResult.Error,
          desc: i18n.get('存在 <span>{__k0}</span> 条错误，<span>{__k1}</span> 条风险', {
            __k0: result.error,
            __k1: result.risk
          })
        }
      }
      return {
        state: AuditResult.Error,
        desc: i18n.get('存在 <span>{__k0}</span> 条错误', { __k0: result.error })
      }
    } else if (result.risk > 0) {
      return {
        state: AuditResult.Warning,
        desc: i18n.get('存在 <span>{__k0}</span> 条风险', { __k0: result.risk })
      }
    } else {
      return {
        state: AuditResult.Success,
        desc: i18n.get('<span>{__k0}</span> 条错误/风险', {
          __k0: 0
        })
      }
    }
  }, [result])

  const cardRenderingTheme = React.useMemo(() => {
    switch (currentAuditResult.state) {
      case AuditResult.Error:
        return Theme.Error
      case AuditResult.Warning:
        return Theme.Warning
      case AuditResult.Success:
        return Theme.Success
      default:
        return Theme.Success
    }
  }, [currentAuditResult])

  const renderingIcon = React.useMemo(() => {
    switch (currentAuditResult.state) {
      case AuditResult.Error:
        return <TwoToneGeneralError fontSize={44} />
      case AuditResult.Warning:
        return <TwoToneGeneralRisk fontSize={44} />
      case AuditResult.Success:
        return <TwoToneGeneralCorrect fontSize={44} />
      default:
        return <TwoToneGeneralCorrect fontSize={44} />
    }
  }, [currentAuditResult.state])

  return (
    <Card className={cx(styles['result-card'], styles[`result-card-${cardRenderingTheme}`])} theme={cardRenderingTheme}>
      <div className={cx(styles.inner, styles[`inner-${cardRenderingTheme}`])}>
        {renderingIcon}
        <div className={styles.content}>
          <p className={styles.title}>{title}</p>
          <p className={styles.subtitle} dangerouslySetInnerHTML={{ __html: currentAuditResult.desc }} />
        </div>
      </div>
      {extra && <div className={styles.extra}>{extra}</div>}
    </Card>
  )
}

export default ResultCard

import React, { useEffect, useState } from 'react'
import styles from './WarningOrErrorTips.module.less'
import { renderAttachments } from '../../../elements/ekbc-business/bills/flow-log-item'
import { AttachmentIF, fetchAttachment } from '../../../components/utils/FetchAttachment'
import { FilledTipsWarning } from '@hose/eui-icons'

export function getSensitiveContent(value: string) {
  return `${i18n.get('提交人添加了补充说明：')}${value}`
}

type Props = {
  content: string
  attachments: AttachmentIF[]
}

const WarningSensitive: React.FC<Props> = ({ content, attachments }) => {
  const [values, setValues] = useState(() => attachments)

  useEffect(() => {
    fnFormatAttachments(attachments).then(result => {
      setValues(result)
    })
  }, [attachments])

  const fnFormatAttachments = async (attachments: AttachmentIF[] = []) => {
    if (!attachments?.length) {
      return []
    }
    const dpAttachments = attachments.filter(value => value?.key?.startsWith('DP:'))
    const noDPAttachments = attachments.filter(value => !value?.key?.startsWith('DP:'))
    const noDPAttachmentsResult = await fetchAttachment(noDPAttachments)
    return [...noDPAttachmentsResult, ...dpAttachments,]
  }

  if (!content && !attachments?.length) return null
  return (
    <div className={styles['bill-warning-bg']}>
      <div className="header-wrapper">
        <FilledTipsWarning fontSize={16} />
        <div className="header-child">{getSensitiveContent(content || '')}</div>
        {values?.length > 0 && (
          <div className="header-right" style={{ width: 'auto' }}>
            {renderAttachments(values, { placement: 'topLeft', className: 'attachment-wrapper-fix' })}
          </div>
        )}
      </div>
    </div>
  )
}

export default WarningSensitive

.result-card {
  display: flex;
  flex-direction: column;
  .inner {
    display: flex;

    @kinds: success, warning, danger;
    each(@kinds, {
      @color: ~"var(--eui-function-@{value}-500)";
      &.inner-@{value} {
        color: @color;

        .subtitle {
          > span {
            color: @color;
            font: var(--eui-num-head-b2);
          }
        }
      }
    })
      .content {
      display: flex;
      flex-direction: column;
      color: var(--eui-text-title);
      margin-left: 4px;

      p {
        margin: 0;
      }

      .title {
        font: var(--eui-font-body-b1);
      }

      .subtitle {
        display: flex;
        align-items: center;
        font: var(--eui-font-note-r2);
        > span {
          margin: 0 4px;
          padding-bottom: 2px;
          line-height: 24px;
        }
      }
    }
  }
  .extra {
    margin-top: 8px;
  }
}

.result-card-success {
  background: linear-gradient(
    90deg,
    rgba(161, 233, 167, 0.5) 0%,
    rgba(213, 248, 216, 0.5) 50%,
    rgba(232, 255, 234, 0.5) 100%
  );
}

.result-card-warning {
  background: linear-gradient(
    90deg,
    rgba(255, 226, 181, 0.5) 0%,
    rgba(255, 247, 232, 0.5) 47%,
    rgba(255, 247, 232, 0.5) 96%
  );
}

.result-card-danger {
  background: linear-gradient(90deg, rgba(254, 159, 159, 0.2) 5.65%, rgba(253, 195, 195, 0.08) 90.93%);
}

import React from 'react'
import { <PERSON><PERSON>, Toolt<PERSON> } from '@hose/eui'
import { OutlinedDirectionLeft, FilledTipsYes, FilledTipsClose, FilledTipsWarning, OutlinedTipsMaybe } from '@hose/eui-icons'
import { RiskWarningItem } from '../type'
import { getAIAuditSummary, isAIItem } from './utils'
import styles from './ai-audit-result.module.less'
import clx from 'classnames'

export interface AIAuditResultProps {
  items?: RiskWarningItem[]
  onBack?: () => void
}

const AIAuditResult: React.FC<AIAuditResultProps> = ({ items = [], onBack }) => {
  const aiItems = items.filter(isAIItem)
  const { total, successCount, riskCount, errorCount } = getAIAuditSummary(items)

  // Sort audit items by riskLevel: PASS -> ERROR -> WARNING
  const sortedAiItems = React.useMemo(() => {
    const stateOrder = { 'PASS': 1, 'ERROR': 2, 'WARNING': 3 }
    return aiItems.slice().sort((a, b) => {
      const orderA = stateOrder[a.riskLevel] || 4
      const orderB = stateOrder[b.riskLevel] || 4
      return orderA - orderB
    })
  }, [aiItems])

  const handleBack = () => {
    onBack?.()
  }

  const renderAuditItem = (item: RiskWarningItem, index: number) => {
    const getStatusIcon = () => {
      switch (item.riskLevel) {
        case 'PASS':
          return <FilledTipsYes className={styles['status-icon']} style={{ color: 'var(--eui-function-success-500)' }} />
        case 'ERROR':
          return <FilledTipsClose className={styles['status-icon']} style={{ color: 'var(--eui-function-danger-500)' }} />
        case 'WARNING':
          return <FilledTipsWarning className={styles['status-icon']} style={{ color: 'var(--eui-function-warning-500)' }} />
        default:
          return <FilledTipsYes className={styles['status-icon']} style={{ color: 'var(--eui-function-success-500)' }} />
      }
    }

    const messageInfo = (item.messages || []).join('\r\n')

    return (
      <div key={item.id || index} className={styles['audit-item']}>
        <div className={styles['audit-item-content']}>
          {getStatusIcon()}
          <div className={styles['audit-item-text']}>
            <span className={styles['audit-item-text-title']}>{item.controlName}</span>
            {messageInfo && (
              <Tooltip title={messageInfo} placement={'left'}>
                <OutlinedTipsMaybe className={styles['info-icon']} />
              </Tooltip>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={styles['ai-audit-result']}>
      {/* Header */}
      <div className={styles['ai-header']}>
        <Button
          category="text"
          size="mini"
          onClick={handleBack}
          icon={<OutlinedDirectionLeft />}
        >
          {i18n.get('返回')}
        </Button>
      </div>

      {/* Summary Section */}
      <div className={styles['summary-section']}>
        <div className={styles['summary-title']}>
          {i18n.get('AI 共计审核{total}条规则', { total })}
        </div>
        <div className={styles['summary-stats']}>
          <div className={clx(styles['stat-card'], { [styles['stat-card-zero']]: successCount === 0 })}>
            <div className={styles['stat-number']} style={{ color: '#00B42A' }}>
              {successCount}
            </div>
            <div className={styles['stat-label']}>
              {i18n.get('通过')}
            </div>
          </div>
          <div className={clx(styles['stat-card'], { [styles['stat-card-zero']]: errorCount === 0 })}>
            <div className={styles['stat-number']} style={{ color: '#F53F3F' }}>
              {errorCount}
            </div>
            <div className={styles['stat-label']}>
              {i18n.get('错误')}
            </div>
          </div>
          <div className={clx(styles['stat-card'], { [styles['stat-card-zero']]: riskCount === 0 })}>
            <div className={styles['stat-number']} style={{ color: '#FF7D00' }}>
              {riskCount}
            </div>
            <div className={styles['stat-label']}>
              {i18n.get('风险')}
            </div>
          </div>
        </div>
      </div>

      {/* Audit Items List */}
      <div className={styles['audit-list-wrapper']}>
        <div className={styles['audit-list']}>
          {sortedAiItems.map((item, index) => renderAuditItem(item, index))}
        </div>
      </div>
    </div>
  )
}

export default AIAuditResult
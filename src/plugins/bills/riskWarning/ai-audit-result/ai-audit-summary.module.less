.gradient-text {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;

  /* Fallback for unsupported browsers */
  color: #667eea;
}

.ai-audit-summary {
  width: 100%;
  border-radius: 8px;
  padding: 6px 8px;
  display: flex;
  align-items: center;
  cursor: pointer;
  background-color: var(--eui-bg-body);
  font: var(--eui-font-note-r2);
  color:  var(--eui-text-caption);
  .ai-audit-summary-tag {
    background: linear-gradient(95deg, rgba(158, 128, 255, 0.15) 1.51%, rgba(72, 220, 220, 0.15) 98.17%);
    font: var(--eui-font-note-b1);
  }
  .ai-audit-summary-title-text {
    flex-grow: 1;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    margin-left: 4px;
  }
  .ai-audit-summary-detail-text {
    white-space: nowrap;
    word-break: keep-all;
  }
}
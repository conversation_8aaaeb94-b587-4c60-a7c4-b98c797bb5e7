/**
 *  Created by pw on 2021/3/11 下午12:57.
 */

interface TrackBillProps {
  startTime: number
  endTime: number
}

export function trackBillReviewTime(params: TrackBillProps) {
  // trackBill('checkflowTime', '查看单据详情时长统计', 'checkFlowTime', params)
}

export function trackBillListLoadTime(params: TrackBillProps) {
  // trackBill('myflowCardTime', '我的单据卡片视图加载速度', 'myFlowCardTime', params)
}

export function trackLoadBillFlowTime(params: TrackBillProps) {
  trackBill('loadflowTime', '系统打开单据详情时长', 'loadflowTime', params)
}

function trackBill(key: string, actionName: string, eventName: string, params: TrackBillProps) {
}

export function loginTrack() {
  localStorage.removeItem('loginStartTime')
  localStorage.removeItem('loginHomePage')
}

/**
 * 埋点通用函数
 */
export function trackFn(key: string, params: any) {}

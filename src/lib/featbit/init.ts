import { DataSync<PERSON>ode<PERSON><PERSON>, FbClientBuilder, FbClient<PERSON>ore, IUser, UserBuilder } from '@featbit/js-client-sdk'
import { Fetch, Resource } from '@ekuaibao/fetch'
import { uuid } from '@ekuaibao/helpers'

let fbClient: ReturnType<FbClientBuilder['build']>

interface FeatBitEnvironment {
  /**
   * 是否启用featbit，无特定开关没有关系，只表示featbit服务是否可用
   */
  enable: boolean
  /**
   * 客户端密钥
   */
  clientKey: string
  /**
   * Event URL
   */
  eventUrl: string
  /**
   * Streaming URL
   */
  streamUrl: string
  /**
   * Open API 端点
   */
  openapiUrl: string
}

const cacheKey = 'HOSE_FB_ANONYMOUS_ID'

function getAnonymousId() {
  let cacheId = localStorage.getItem(cacheKey)
  if (!cacheId) {
    cacheId = uuid()
    localStorage.setItem(cacheKey, cacheId)
  }
  return cacheId
}

const featBitConfig = () => {
  const clientKey = window.getEnvConfigValue('featBitClientKey', 'uXS-G58nrU-NZeY1rC7NIA6SLgEkNFGUW3G6zT4tMTcQ')
  const featBitDomain = window.getEnvConfigValue('featBitDomain', 'https://feat.hosecloud.com')
  return {
    clientKey,
    eventUrl: featBitDomain,
    openapiUrl: featBitDomain
  }
}

export const initFeatBit = async (user?: IUser) => {
  try {
    if (!user) {
      // @ts-ignore
      const staffId = Fetch.staffId
      const ekbCorpId = Fetch.ekbCorpId
      if (!ekbCorpId) {
        return
      }
      user = new UserBuilder(staffId).custom('corpId', ekbCorpId).build()
    }

    const config = featBitConfig()
    fbClient = new FbClientBuilder()
      .sdkKey(config.clientKey)
      .pollingUri(config.eventUrl)
      .eventsUri(config.openapiUrl)
      .dataSyncMode(DataSyncModeEnum.POLLING)
      .pollingInterval(60000)
      .user(user)
      .build()
      .addListener('error', error => {
        console.log(error)
      })

    await fbClient.waitForInitialization()
  } catch (e) {
    console.log(e)
  }
}

export function switchFeatBitUser() {
  const user = createFBUser(Fetch.staffId, Fetch.staffId, [
    { name: 'staffId', value: Fetch.staffId },
    { name: 'corpId', value: Fetch.ekbCorpId }
  ])
  return fbClient?.identify(user)
}

export const anonymousUser = () => {
  const anonymousId = getAnonymousId()
  return createFBUser(anonymousId, anonymousId)
}

const createFBUser = (
  keyId: string,
  name: string,
  customizedProperties: Array<{ name: string; value: string }> = []
) => {
  const anonymousId = getAnonymousId()
  const properties = customizedProperties.concat([
    { name: 'anonymousId', value: anonymousId },
    { name: 'clientCategory', value: 'web' },
    { name: 'domain', value: location.host },
    { name: 'pathname', value: location.pathname }
  ])
  const user = new UserBuilder(keyId).name(name)
  properties.forEach(({ name, value }) => {
    user.custom(name, value)
  })
  return user.build()
}

export const getVariation = (
  flagKey: Parameters<FbClientCore['variation']>[0],
  defaultValue: Parameters<FbClientCore['variation']>[1]
): ReturnType<FbClientCore['variation']> => {
  return fbClient?.variation(flagKey, defaultValue) || defaultValue
}

export const getBoolVariation = (flagKey: string, defaultValue = false): ReturnType<FbClientCore['boolVariation']> => {
  return fbClient?.boolVariation(flagKey, defaultValue) || defaultValue
}

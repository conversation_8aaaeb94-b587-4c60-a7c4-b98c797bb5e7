/*!
 * Copyright 2019 yangjunbao <<EMAIL>>. All rights reserved.
 * @since 2019-07-09 12:02:42
 */

import { app } from '@ekuaibao/whispered'
import { addNcpcCode } from '@ekuaibao/lib/lib/addNcpcCode'
import { handleError } from '../../lib/lib-util'
import { callback } from '../index.v0'
import { newHomeConfig } from '../newHomeConfig'
import BrowserSDK from '@ekuaibao/sdk-bridge/sdk/browser'
import registerAPI from '../registerAPI'
import initializeTitle from '../initializeTitle'
import registerHandler from '../registerHandler'
import initPlatformInfo from '../initPlatformInfo'
import systemLimitLogout from '../systemLimitLogout'
import { fetchHandleError } from '../FetchConfig'
import { initRoute } from '../initRoute'
import initLogger from '../../logger/initLogger'
import { initColors } from '../initColors'
import { initDatafluxRum } from "../dataflux-rum";

/********注册平台类型*******/
window.__PLANTFORM__ = 'APP'

initDatafluxRum();
initLogger()

function startup() {
  app.use({
    id: '@app',
    onready() {
      addNcpcCode()
    }
  })
  callback()
}

fetchHandleError(handleError)

export async function bootstrap() {
  initColors()
  initRoute()
  initPlatformInfo()
  systemLimitLogout()
  app.container.set('IDeviceType', 'DESKTOP')
  app.container.set('ISMessageEntry', false)
  app.sdk = app.container.get(BrowserSDK)

  registerHandler()

  startup()
  registerAPI()

  try {
    await (app.sdk as BrowserSDK).initialize()
  } catch (e) {
    await newHomeConfig()
    return
  }
  await newHomeConfig()

  initializeTitle()
}

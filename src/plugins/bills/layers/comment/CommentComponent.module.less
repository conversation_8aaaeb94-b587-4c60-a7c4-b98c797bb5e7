.mentions {
  margin: 10px 0;
  min-height: 240px;
  margin-bottom: 0;
  font: var(--eui-font-body-r1);
  .mentions--multiLine .mentions__control {
    font: var(--eui-font-body-r1);
  }
  .mentions__highlighter {
    border: 1px solid transparent;
    box-sizing: border-box;
    overflow: auto;
    min-height: 240px;
    padding: 8px;
  }
  .mentions__input {
    border: 1px solid var(--eui-line-border-component) !important;
    border-radius: 6px;
    padding: 8px;
    outline: var(--eui-primary-pri-500) !important;
    min-height: 240px;
  }
  .mentions__input:focus {
    border-color: var(--eui-primary-pri-500) !important;
    outline: var(--eui-primary-pri-500) !important;
  }
  .mentions__input:hover {
    border-color: var(--eui-primary-pri-500) !important;
    outline: var(--eui-primary-pri-500) !important;
  }

  .mentions__suggestions__list {
    background-color: #FFF;
    font-size: 12px;
    border-radius: 6px;
    padding: 4px !important;
    max-height: 186px;
    overflow: auto;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, .15);
  }

  .mentions__suggestions__item {
    padding: 2px 10px;
    border-radius: 4px;
    margin-bottom: 2px;
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
    min-width: 130px;
    display: flex;
    align-items: center;
  }

  .mentions__suggestions__item--focused {
    background-color: var(--brand-1);
  }
  

  .mentions__mention {
    position: relative;
    z-index: 1;
    color: var(--eui-primary-pri-500);
    text-shadow: 1px 1px 1px white, 1px -1px 1px white, -1px 1px 1px white,
      -1px -1px 1px white;
  }
}

.characterCount {
  color: var(--eui-text-placeholder);
  font: var(--eui-font-note-r2);
  text-align: right;
}

/**************************************
 * Created By LinK On 2019/5/20 21:32.
 **************************************/

// 声明 window.Sentry 类型
declare global {
  interface Window {
    Sentry: any
  }
}

export default initSentry

// 创建 Sentry 下线提示函数
function createSentryDeprecationError(methodName: string) {
  const errorMessage = `Sentry SDK 已下线，请移除对 window.Sentry.${methodName} 的调用。如需错误监控，请使用观测云监控服务。`
  console.error(errorMessage)
  throw new Error(errorMessage)
}

// 创建 Sentry 代理对象
function createSentryProxy() {
  return new Proxy({}, {
    get(_target, prop) {
      // 对于任何属性访问，返回一个会抛出错误的函数
      return function(..._args: any[]) {
        createSentryDeprecationError(String(prop))
      }
    },

    set(_target, prop, _value) {
      // 阻止设置属性，并提示错误
      createSentryDeprecationError(`${String(prop)} (setter)`)
      return false
    },

    has(_target, _prop) {
      // 让外部代码认为属性存在，避免提前的 undefined 检查
      return true
    },

    ownKeys(_target) {
      // 返回常见的 Sentry 方法名，让外部代码认为这些方法存在
      return ['captureException', 'captureMessage', 'addBreadcrumb', 'setUser', 'setTag', 'setContext', 'configureScope', 'withScope', 'init']
    },

    getOwnPropertyDescriptor(_target, prop) {
      // 让外部代码认为属性是可配置和可枚举的
      return {
        enumerable: true,
        configurable: true,
        value: function(..._args: any[]) {
          createSentryDeprecationError(String(prop))
        }
      }
    }
  })
}

// 初始化Sentry
export function initSentry(): void {
  window.Sentry = createSentryProxy()

  try {
  } catch {}
}
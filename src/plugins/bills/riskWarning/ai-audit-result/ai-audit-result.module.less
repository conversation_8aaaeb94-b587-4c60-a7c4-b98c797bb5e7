.ai-audit-result {
  background-color: var(--eui-bg-body);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0 16px;
  height: 100%;

  .ai-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0 8px;
    color: var(--eui-text-title);
  }

  .summary-section {
    background: var(--eui-bg-body-overlay);
    border-radius: 8px;
    padding: 12px;

    .summary-title {
      color: var(--eui-text-title);
      font: var(--eui-font-body-b1);
      margin-bottom: 8px;
    }

    .summary-stats {
      display: flex;
      gap: 8px;
      justify-content: space-between;

      .stat-card {
        flex: 1;
        background: var(--eui-bg-body);
        border-radius: 4px;
        height: 54px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        &.stat-card-zero {
          .stat-number {
            color: var(--eui-text-disabled) !important;
          }
        }

        .stat-number {
          font: var(--eui-num-head-b2);
          margin-bottom: -2px;
        }

        .stat-label {
          font: var(--eui-font-note-b2);
          color: var(--eui-text-title);
          white-space: nowrap;
        }
      }
    }
  }

  .audit-list-wrapper {
    max-height: calc(100% - 180px);
    overflow: auto;
  }

  .audit-list {
    background: #f7f8fa;
    border-radius: 8px;
    padding: 12px;

    .audit-item {
      display: flex;
      flex-direction: column;
      gap: 4px;

      &:not(:last-child) {
        margin-bottom: 8px;
      }

      .audit-item-content {
        display: flex;
        align-items: center;
        gap: 4px;

        .status-icon {
          flex-shrink: 0;
          align-self: flex-start;
          margin-top: 3px;
        }

        .audit-item-text {
          flex: 1;
          color: var(--eui-text-title);
          font: var(--eui-font-body-r1);
        }

        .info-icon {
          color: var(--eui-icon-n2);
          flex-shrink: 0;
          float: right;
          margin-top: 3px;
        }
      }
    }
  }
}
